// const fs = require('fs')
// const path = require('path')
// const { execSync } = require('child_process')

// const scopes = fs
//   .readdirSync(path.resolve(__dirname, 'src'), { withFileTypes: true })
//   .filter(dirent => dirent.isDirectory())
//   .map(dirent => dirent.name.replace(/s$/, ''))

// // precomputed scope
// const scopeComplete = execSync('git status --porcelain || true')
//   .toString()
//   .trim()
//   .split('\n')
//   .find(r => ~r.indexOf('M  src'))
//   ?.replace(/(\/)/g, '%%')
//   ?.match(/src%%((\w|-)*)/)?.[1]
//   ?.replace(/s$/, '')

module.exports = {
  userEmoji: true,
  // ignores: [commit => commit.includes('init')],
  extends: ['git-commit-emoji'],
  rules: {
    // 'header-max-length': [2, 'always', 108],
  },
  prompt: {
    /** @use `pnpm commit :f` */
    // customScopesAlign: !scopeComplete ? 'top' : 'bottom', //  如果 scope 不完整，则将 customScopesAlign 设置为 top，否则设置为 bottom
    // defaultScope: scopeComplete, //  如果 scope 完整，则将 defaultScope 设置为 true，否则设置为 false
    // scopes: [...scopes, 'mock'],
    allowEmptyIssuePrefixs: false, //  不允许空 issue 前缀
    allowCustomIssuePrefixs: false, //  不允许自定义 issue 前缀
    // emptyScopesAlias: 'empty: 不填写',
    // customScopesAlias: 'custom: 自定义',
    skipQuestions: ['scope', 'body', 'breaking', 'footer'], // 跳过 body、breaking、footer 三个问题
    userEmoji: true,
    emojiAlign: 'left',

    messages: {
      type: '选择你要提交的类型 :',
      // scope: '选择一个提交范围 (可选):',
      // customScope: '请输入自定义的提交范围 :',
      subject: '填写简短精炼的变更描述 :\n',
      // body: '填写更加详细的变更描述 (可选)。使用 "|" 换行 :\n',
      // breaking: '列举非兼容性重大的变更 (可选)。使用 "|" 换行 :\n',
      // footerPrefixsSelect: '选择关联issue前缀 (可选):',
      // customFooterPrefixs: '输入自定义issue前缀 :',
      // footer: '列举关联issue (可选) 例如: #31, #I3244 :\n',
      confirmCommit: '是否提交或修改commit ?'
    },

    typeEnum: [
      'feat',
      'fix',
      'perf',
      'refactor',
      'style',
      'format',
      'fire',
      'publish',
      'tag',
      'config',
      'chore',
      'docs',
      'init',
      'patch',
      'file',
      'test',
      'git'
    ],

    types: [
      { value: 'feat', name: '✨ feat: 引入新功能', emoji: '✨' },
      { value: 'fix', name: '🐛 fix: 修复bug', emoji: '🐛' },
      { value: 'perf', name: '⚡ perf: 性能优化', emoji: '⚡' },
      { value: 'refactor', name: '🎨 refactor: 重构代码', emoji: '🎨' },
      { value: 'style', name: '💄 style: 更新UI样式', emoji: '💄' },
      { value: 'format', name: '🥚 format: 格式化代码', emoji: '🥚' },
      { value: 'fire', name: '🔥 fire: 删除冗余代码', emoji: '🔥' },
      { value: 'publish', name: '🚀 publish: 发布新版本', emoji: '🚀' },
      { value: 'tag', name: '📌 tag: 添加版本标签', emoji: '📌' },
      { value: 'config', name: '🔧 config: 更新配置文件', emoji: '🔧' },
      { value: 'chore', name: '🧹 chore: 日常维护', emoji: '🧹' },
      { value: 'docs', name: '📝 docs: 更新文档', emoji: '📝' },
      { value: 'init', name: '🎉 init: 初始化项目', emoji: '🎉' },
      { value: 'patch', name: '🚑 patch: 重要补丁修复', emoji: '🚑' },
      { value: 'file', name: '📦 file: 文件操作', emoji: '📦' },
      { value: 'test', name: '✅ test: 增加测试代码', emoji: '✅' },
      { value: 'git', name: '🙈 git: 更新忽略文件', emoji: '🙈' }
    ]
  }
}
