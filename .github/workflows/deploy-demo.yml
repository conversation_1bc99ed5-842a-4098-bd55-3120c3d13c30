name: Deploy Pages Demo

on:
  push:
    branches: ['master']

  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      # 设置服务器时区为东八区
      - name: Set time zone
        run: sudo timedatectl set-timezone 'Asia/Shanghai'
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with:
          version: 8
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm i --no-frozen-lockfile
      - name: Build
        run: pnpm build:h5
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./dist/build/h5

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4