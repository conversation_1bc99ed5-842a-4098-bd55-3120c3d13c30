import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  // 你也可以定义 pages 字段，它具有最高的优先级。
  pages: [],
  // tabBar需要手动配置，插件不会自动生成
  tabBar: {
    color: '#D9D9D9',
    selectedColor: '#FF3C29',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    iconWidth: '22px',
    list: [
      {
        pagePath: 'pages/tab/home/<USER>',
        iconPath: 'static/images/tabbar/home.png',
        selectedIconPath: 'static/images/tabbar/home-select.png',
        text: '首页'
      },
      {
        pagePath: 'pages/tab/shop/index',
        iconPath: 'static/images/tabbar/shop.png',
        selectedIconPath: 'static/images/tabbar/shop-select.png',
        text: '购物车'
      },
      {
        pagePath: 'pages/tab/user/index',
        iconPath: 'static/images/tabbar/user.png',
        selectedIconPath: 'static/images/tabbar/user-select.png',
        text: '我的'
      }
    ]
  },
  globalStyle: {
    // 导航栏字体颜色
    navigationBarTextStyle: '@navTxtStyle',
    // 导航栏背景色
    navigationBarBackgroundColor: '@navBgColor',
    // 导航栏背景底色
    backgroundColor: '@bgColor',
    // 禁止下拉刷新
    enablePullDownRefresh: false,
    // 禁止上拉触底。设置为一个较大的值，以变相禁用
    onReachBottomDistance: 5000,
    'app-plus': {
      scrollIndicator: 'none'
    },
    // 页面跳转动画
    animationType: 'slide-in-right'
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)': 'z-paging/components/z-paging$1/z-paging$1.vue'
    }
  }
})
