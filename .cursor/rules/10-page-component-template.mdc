---
description: 
globs: src/pages/**/*.vue
alwaysApply: false
---
---
description: 页面组件结构与模板规范指南
applicable_paths: ["src/pages/**/*.vue"]
---

# 页面组件模板规范

## 页面组件结构顺序

页面组件必须按照以下顺序组织代码，确保结构一致性：

1. `<route>` - 路由配置块
2. `<template>` - 模板块
3. `<script setup>` - 组合式API脚本块
4. `<style>` - 样式块

```vue
<!-- 标准页面组件结构 -->
<route lang="json5" type="page">
{
  // 路由配置
}
</route>

<template>
  <!-- 页面模板 -->
</template>

<script setup lang="ts">
// 组合式API（主要逻辑）
</script>

<style lang="scss" scoped>
/* 样式（仅在UnoCSS无法满足需求时使用） */
</style>
```

## 路由配置规范

每个页面**必须**包含 `<route>` 块，定义页面配置：

- **必须**使用 `lang="json5"` 属性
- 可以使用 `type` 属性标识页面类型（如 `type="home"`）
- **必须**包含以下配置：

```vue
<route lang="json5">
{
  // 布局配置（可选）
  layout: "default", // 布局组件,可选值 theme,default

  // 页面样式（必须）
  style: {
    navigationBarTitleText: "页面标题", // 页面标题
    navigationStyle: "default", // 导航栏样式，可选值：default（默认）、custom（自定义）
  },
}
</route>
```

## 页面模板规范

- 模板根元素应使用 `<view>` 组件
- 优先使用UnoCSS原子类构建布局和样式
- 避免在模板中使用复杂表达式
- 组件使用kebab-case命名方式（如 `<goods-item>`）
- 合理注释

```vue
<template>
  <view class="page-container flex flex-col bg-gray-100 min-h-screen">
    <!-- 页面内容 -->
    <view class="p-4">
      <goods-item v-for="item in goodsList" :key="item.id" :goods="item" />
    </view>
  </view>
</template>
```

## 脚本规范

- 使用组合式API（`<script setup>`）
- 如需定义布局或其他选项，添加选项式API块
- 较为严格的定义TypeScript类型
- 组件名称使用 `defineOptions` 定义

```vue
<script setup lang="ts">
// 组件名称定义
defineOptions({
  name: "PageName",
});

// 导入依赖（尽量减少，利用auto-import）
import { ref } from "vue";
import type { Goods } from "@/types";

// 状态定义
const loading = ref(false);
const goodsList = ref<Goods[]>([]);

// 生命周期钩子
onLoad((options) => {
  // 页面加载逻辑
  fetchData();
});

// 方法定义
async function fetchData() {
  try {
    loading.value = true;
    // API调用逻辑
  } catch (error) {
    // 错误处理
  } finally {
    loading.value = false;
  }
}
</script>
```

## 样式规范

- 优先使用UnoCSS原子类
- 只有当UnoCSS无法满足需求时，才使用自定义样式
- 样式必须添加 `scoped` 属性
- 使用 SCSS 预处理器
- 单位默认使用rpx而非px

```vue
<style lang="scss" scoped>
/* 仅当UnoCSS无法满足需求时添加自定义样式 */
.custom-component {
  &__header {
    // 自定义样式
  }
}
</style>
```

## 页面生命周期处理

- 使用 UniApp 生命周期钩子 (`onLoad`, `onShow` 等)
- 避免在页面组件中编写大量业务逻辑，应提取到 hooks 或 store 中
- 数据获取应在 `onLoad` 或 `onShow` 中进行，并处理加载状态和错误情况

## 页面组件间通信

- 页面间传参优先使用路由参数
- 复杂状态应使用 Pinia store 管理
- 避免使用全局事件总线进行通信
- 临时事件可使用 `uni.$on`/`uni.$emit`，但必须在组件卸载时解绑

```typescript
// 页面通信示例
// 发送方
const goToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/detail/index?id=${id}`,
  });
};

// 接收方
onLoad((options) => {
  const { id } = options;
  if (id) {
    fetchDetail(id);
  }
});
```

## 性能优化

- 避免在模板中使用复杂表达式
- 大列表使用虚拟滚动或分页加载
- 使用 `z-paging` 组件实现下拉刷新和上拉加载
- 避免频繁触发计算属性和监听器
- 合理使用 `v-memo` 减少不必要的重渲染
