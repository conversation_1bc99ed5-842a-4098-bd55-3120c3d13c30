---
description: 
globs: **/*.{vue,ts,js}
alwaysApply: false
---
---
description: 项目概述与技术栈说明
applicable_paths: ["**/*.{vue,ts,js}"]
---

# 项目概述

`uni-plus` 是一个基于 Vue 3、TypeScript、UniApp 的跨平台应用开发模板。

## 技术栈

- Vue 3.4+
- TypeScript 4.9+
- UniApp 框架
- Pinia 状态管理
- UnoCSS 原子化 CSS
- Wot-Design-Uni UI 组件库

## 项目特点

- 跨平台：支持 H5、iOS、Android、各类小程序等多平台
- 统一路由和拦截器管理
- HTTP 请求封装和拦截器
- 自动化组件和页面导入
- 权限控制系统
- 多语言国际化支持
- 代码规范和格式化配置

## 描述和适用路径元数据

- 此文档提供了 `uni-plus` 项目的概述，包括技术栈、项目特点等信息。
- 适用路径：`/docs/project-overview`
