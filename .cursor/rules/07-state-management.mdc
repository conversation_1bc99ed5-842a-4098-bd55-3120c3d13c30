---
description: 
globs: src/store/**/*.ts", "src/components/**/*.vue", "src/pages/**/*.vue
alwaysApply: false
---
---
description: Pinia状态管理规范与最佳实践
applicable_paths: ["src/store/**/*.ts", "src/components/**/*.vue", "src/pages/**/*.vue"]
---

# 状态管理规范

## Pinia Store 组织

- Store 文件放置在 `src/store/` 目录下
- 按功能模块拆分 Store，如 `user.ts`、`cart.ts` 等
- 每个 Store 应该职责单一，只处理一个领域的状态
- 使用 pinia-plugin-persistedstate 实现状态持久化

## Store 定义规范

- 使用 `defineStore` 创建 Store
- 为 State 提供明确的类型定义
- Getters 和 Actions 使用箭头函数语法
- 异步操作应在 Actions 中处理

```typescript
// Store 定义示例
import { defineStore } from 'pinia'
import type { User } from '@/types'
import { getUserInfo, updateUserProfile } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null as User | null,
    token: '',
    permissions: [] as string[]
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    username: (state) => state.user?.username || '',
    hasPermission: (state) => (perm: string) => state.permissions.includes(perm)
  },
  
  actions: {
    async login(username: string, password: string) {
      // 登录逻辑
    },
    
    async fetchUserInfo() {
      // 获取用户信息
    },
    
    logout() {
      // 登出逻辑
    }
  },
  
  persist: {
    key: 'user-storage',
    paths: ['token', 'user']
  }
})
```

## Store 使用规范

- 在组件中使用 `useStore` 钩子获取 Store 实例
- 使用 `storeToRefs` 获取响应式状态
- 避免直接修改 Store 状态，应通过 Actions 修改
- 在组件卸载时清理不需要的事件监听

```vue
<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'

// 获取 Store 和响应式状态
const userStore = useUserStore()
const { user, isLoggedIn } = storeToRefs(userStore)

// 调用 Actions
const handleLogin = async () => {
  await userStore.login(username.value, password.value)
  // 登录后逻辑
}
</script>
```

## 状态持久化

- 敏感数据不应持久化，如密码等
- 明确指定需要持久化的状态字段
- 为持久化状态设置合理的过期时间
- 考虑多平台差异，适配不同平台的存储机制
