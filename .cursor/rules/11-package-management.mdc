---
description: 
globs: package.json", "pnpm-lock.yaml
alwaysApply: false
---
---
description: PNPM包管理工具使用规范
applicable_paths: ["package.json", "pnpm-lock.yaml"]
---

# 包管理工具规范

## PNPM 作为默认包管理工具

项目使用 PNPM 作为默认的包管理工具，这样做的理由包括：

- 节省磁盘空间：通过硬链接共享依赖
- 更快的安装速度：并行安装和更高效的缓存机制
- 更严格的依赖管理：避免幽灵依赖问题
- 支持 monorepo：内置工作空间(workspace)功能

## 常用命令

```bash
# 安装所有依赖
pnpm install

# 添加依赖
pnpm add <package-name>

# 添加开发依赖
pnpm add -D <package-name>

# 更新依赖
pnpm update <package-name>

# 运行脚本
pnpm run <script-name>
```

## 锁文件维护

- 确保将 `pnpm-lock.yaml` 文件提交到版本控制系统
- 不要手动编辑锁文件
- 团队成员应使用相同版本的 pnpm 以确保一致性

## 版本控制

- 所有依赖版本应在 `package.json` 中明确指定
- 使用 `^` 前缀允许补丁和小版本更新
- 对于关键依赖，可以固定版本号（无前缀）以避免意外更新

## 发布与构建

- 使用 `pnpm publish` 发布包
- 使用 `pnpm build` 构建项目
- 确保在发布前执行 `pnpm run lint` 和 `pnpm run test`
