---
description: 
globs: src/pages/**/*.vue", "src/interceptors/router.ts", "src/pages.json
alwaysApply: false
---
---
description: 路由和页面开发规范指南
applicable_paths: ["src/pages/**/*.vue", "src/interceptors/router.ts", "src/pages.json"]
---

# 路由和页面开发规范

## 页面文件组织

- 页面文件放置在 `src/pages/` 目录下
- 根据功能模块组织页面，如 `user/`、`goods/` 等
- 每个页面一个目录，包含页面组件和相关资源
- 页面组件命名为 `index.vue`

```
src/pages/
  ├── index/              # 首页模块
  │   └── index.vue       # 首页页面组件
  ├── user/               # 用户模块
  │   ├── index.vue       # 用户中心页面
  │   ├── login.vue       # 登录页面
  │   └── register.vue    # 注册页面
  └── goods/              # 商品模块
      ├── index.vue       # 商品列表页面
      └── detail.vue      # 商品详情页面
```

## 路由配置

- 路由配置在页面文件的 `<route>` 块中定义
- 使用 `lang="json5"` 属性定义JSON5格式的路由配置
- 路由配置自动合并到 [src/pages.json](mdc:src/pages.json) 文件中
- 路由拦截和权限控制在 [src/interceptors/router.ts](mdc:src/interceptors/router.ts) 中处理

## 页面组件结构顺序

页面组件必须按照以下顺序组织代码：

1. `<route>` - 路由配置块
2. `<template>` - 模板块 
3. `<script>` - 选项式API脚本块（可选）
4. `<script setup>` - 组合式API脚本块
5. `<style>` - 样式块

```vue
<!-- 正确的页面组件结构 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: "页面标题"
  },
  meta: {
    auth: true
  }
}
</route>

<template>
  <!-- 页面内容 -->
</template>

<script lang="ts">
export default {
  layout: 'default'
}
</script>

<script setup lang="ts">
// 页面逻辑
</script>

<style lang="scss" scoped>
/* 样式 */
</style>
```

## 页面组件规范

- 页面组件应使用 `<script setup>` 语法
- 复杂页面逻辑应拆分为独立的 hooks
- 页面状态管理应使用 Pinia store
- 页面组件应专注于组合和协调子组件，而非包含大量业务逻辑

## 布局使用

- 通用布局组件位于 `src/layouts/` 目录下
- 页面通过选项式API的 `layout` 属性指定使用的布局
- 布局组件负责处理通用的页面结构，如导航、页头页脚等

## 页面跳转

- 使用 UniApp 提供的导航 API 进行页面跳转
- 路径使用相对路径，避免硬编码完整路径
- 跳转参数应类型安全，避免直接拼接字符串

```typescript
// 页面跳转示例
const goToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/goods/detail?id=${id}`
  })
}
```

## 路由类型

根据业务需求，可以使用不同类型的路由：

- 普通页面：通过 `navigateTo` 打开，可以返回
- 重定向页面：通过 `redirectTo` 打开，替换当前页面
- 重启页面：通过 `reLaunch` 打开，关闭所有页面
- Tab页面：通过 `switchTab` 打开，切换到已存在的Tab页面
