---
description: 
globs: **/*.{vue,ts,js,md}
alwaysApply: false
---
---
description: 中文语言使用规范与国际化处理指南
applicable_paths: ["**/*.{vue,ts,js,md}"]
---

# 中文语言规则

## AI 响应规范

- 所有对用户的响应均使用中文
- 代码注释使用中文，提高可读性
- 技术术语保持英文原样，不强制翻译
- 错误提示和界面文本应使用中文

## 注释规范

- 代码逻辑复杂处应添加中文注释
- 函数和类应添加中文功能说明
- API接口应添加中文参数说明
- 重要算法应有详细中文注释

```typescript
/**
 * 获取用户信息
 * @param userId 用户ID
 * @returns 用户信息对象
 */
export function getUserInfo(userId: string) {
  // 通过用户ID获取信息
  return http.get<UserInfo>(`/api/user/${userId}`)
}
```

## 提交信息规范

- Git 提交信息使用中文描述变更内容
- 遵循 emoji 提交规范，如：
  - `✨ feat: 新增登录功能`
  - `🐞 fix: 修复购物车计算错误`
  - `📃 docs: 更新README文档`

## 变量命名规范

- 变量、函数、类等命名使用英文（遵循项目命名规范）
- 可在注释中添加中文说明
- 避免使用拼音或中英文混合作为标识符

## 国际化处理

- 界面文本使用国际化配置，支持中英文切换
- 错误消息应使用国际化配置
- 日期、货币等格式化应考虑本地化
