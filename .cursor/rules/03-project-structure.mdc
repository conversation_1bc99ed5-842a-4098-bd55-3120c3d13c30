---
globs: **/*
alwaysApply: false
---

---

description: 项目目录结构与文件组织规范
applicable_paths: ["**/*"]

---

# 项目结构规范

## 目录结构

- `src/` - 源代码目录
  - `api/` - API 接口定义
  - `components/` - 通用组件
  - `hooks/` - 可复用的逻辑（自定义 hooks）
  - `http/` - HTTP 请求相关配置
  - `interceptors/` - 请求和路由拦截器
  - `layouts/` - 布局组件
  - `locale/` - 国际化语言包
  - `pages/` - 页面文件
  - `static/` - 静态资源
  - `store/` - Pinia 状态管理
  - `types/` - TypeScript 类型定义
  - `uni_modules/` - UniApp 插件
  - `utils/` - 工具函数

## 关键文件

- [src/main.ts](mdc:src/main.ts) - 应用入口文件
- [src/App.vue](mdc:src/App.vue) - 根组件
- [src/pages.json](mdc:src/pages.json) - 页面路由配置
- [src/manifest.json](mdc:src/manifest.json) - 应用配置
- [src/uni.scss](mdc:src/uni.scss) - 全局样式变量

## 导入规范

- 相对路径导入：使用 `@/` 作为 src 目录的别名
- 优先使用命名导入而非默认导入
- 按以下顺序组织导入：
  1. Vue 核心库
  2. 第三方库
  3. 项目内组件/工具/hooks
  4. 类型导入
  5. 样式导入

```typescript
// 导入示例
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'
import type { UserInfo } from '@/types'
import '@/styles/common.scss'
```
