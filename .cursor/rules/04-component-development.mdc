---
description: 
globs: src/components/**/*.vue
alwaysApply: false
---
---
description: 组件开发设计原则与最佳实践
applicable_paths: ["src/components/**/*.vue"]
---

# 组件开发规范

## 组件设计原则

- 单一职责：一个组件只负责一个功能
- 高内聚低耦合：组件内部逻辑紧密相关，对外依赖最小化
- 可重用性：设计通用组件，避免重复代码
- 可测试性：组件逻辑应易于测试

## 组件示例

### 商品项组件示例

[src/components/goods-item.vue](mdc:src/components/goods-item.vue) 是一个遵循项目规范的组件示例。

组件结构要点：
- 明确的组件名称定义（使用 defineOptions）
- 类型安全的 props 定义
- 清晰的模板结构和样式
- 功能聚焦，职责单一

## Props 定义规范

- 必须为所有 props 定义类型和默认值
- 使用 TypeScript 类型而非 Vue 的运行时类型
- 为必须属性添加 `required: true`
- 为可选属性提供合理的默认值

```typescript
// Props 定义示例
defineProps({
  title: {
    type: String,
    required: true
  },
  showIcon: {
    type: Boolean,
    default: false
  },
  items: {
    type: Array as PropType<Item[]>,
    default: () => []
  }
})
```

## 事件处理

- 事件命名使用 kebab-case（如：`item-click`）
- 使用 `defineEmits` 定义组件触发的事件
- 事件处理函数命名规范：`handle` + 事件名（如：`handleItemClick`）

```typescript
// 事件定义示例
const emit = defineEmits<{
  'item-click': [id: string]
  'item-delete': [id: string, index: number]
}>()

// 事件处理函数
const handleItemClick = (id: string) => {
  emit('item-click', id)
}
```
