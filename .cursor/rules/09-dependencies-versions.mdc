---
description: 
globs: package.json", "src/**/*.{vue,ts,js}
alwaysApply: false
---
---
description: 项目依赖版本与第三方库使用规范
applicable_paths: ["package.json", "src/**/*.{vue,ts,js}"]
---

# 依赖版本与三方库规范

## 核心库版本

项目使用以下版本的核心库，生成代码时必须与这些版本保持兼容：

- Vue: ^3.4.21
- TypeScript: ^4.9.4
- Pinia: ^2.3.0
- UnoCSS: 0.65.2

## UI组件库

项目使用 Wot Design Uni 作为主要UI组件库：

```typescript
// Wot Design Uni 版本
"wot-design-uni": "^1.10.0"
```

使用组件库时需注意：

- 优先使用 WotResolver 自动导入组件
- 组件名称前缀为 `wd-`，如 `<wd-button>`、`<wd-icon>`
- 参考组件库文档确认API，避免使用已弃用特性

## UnoCSS 优先原则

- **最高优先级**：使用 UnoCSS 原子化 CSS 构建界面
- 只有在原子化 CSS 无法满足需求时，才使用组件自定义样式
- 保持样式简洁，避免复杂的样式嵌套
- 常用的原子类包括：
  - 布局：`flex`, `grid`, `block`, `inline-block`
  - 间距：`m-`, `p-`, `mx-`, `my-`, `px-`, `py-`
  - 尺寸：`w-`, `h-`, `min-w-`, `max-w-`
  - 字体：`text-`, `font-`, `leading-`
  - 颜色：`text-`, `bg-`, `border-`

```html
<!-- UnoCSS 使用示例 -->
<view class="flex flex-col gap-4 p-4 bg-white rounded-lg">
  <text class="text-lg font-bold text-gray-900">标题</text>
  <view class="w-full h-0.5 bg-gray-200"></view>
  <text class="text-sm text-gray-600">内容描述</text>
</view>
```

## Auto Import 使用规范

项目配置了自动导入以下内容：

- Vue API (ref, reactive, computed 等)
- UniApp API (uni.request 等)
- 项目自定义 Hooks (`src/hooks` 目录下的函数)

注意事项：

- 避免显式导入已自动导入的内容
- 检查 `src/types/auto-import.d.ts` 确认哪些 API 已自动导入
- 非页面组件不要依赖自动导入，应显式导入需要的依赖
- 自定义 hooks 要放在 `src/hooks` 目录下才能被自动导入

## 路由与页面配置

页面必须包含 route 块来定义路由配置：

```vue
<route lang="json5">
{
  style: {
    navigationBarTitleText: "页面标题",
    enablePullDownRefresh: false
  },
  meta: {
    auth: true, // 是否需要登录
    tabBar: false // 是否是tabBar页面
  }
}
</route>
```

- 使用 `lang="json5"` 确保正确格式化
- 路由拦截依赖 meta 中的 auth 字段判断权限

## 其他重要依赖版本

- echarts: ^5.6.0 (图表库)
- lodash-es: ^4.17.21 (工具函数库)
- z-paging: ^2.8.4 (分页组件)

## TypeScript 严格类型

- 开启严格模式检查 (`strict: true`)
- 所有函数参数和返回值必须有明确类型
- 使用接口或类型别名定义复杂数据结构
- 避免使用 `any` 类型，优先使用 `unknown`
- 使用类型断言时，必须确保类型正确

```typescript
// 正确的类型定义示例
interface UserInfo {
  id: string;
  name: string;
  age?: number;
  roles: string[];
}

function getUserById(id: string): Promise<UserInfo> {
  return http.get<UserInfo>(`/api/user/${id}`);
}
```
