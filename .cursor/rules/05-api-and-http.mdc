---
description: API接口定义与HTTP请求封装规范
applicable_paths: ["src/api/**/*.ts", "src/http/**/*.ts", "src/types/**/*.ts"]
---

# API 和 HTTP 请求规范（新版）

## API 定义

- API 接口按模块分类组织在 `src/api/` 目录下
- 每个模块对应一个文件，如 `user.ts`、`goods.ts` 等
- 使用 TypeScript 类型定义请求参数和响应数据结构
- 使用函数封装 API 请求，便于复用和维护
- 统一通过 `@/http/requestWrapper` 导入的 `http` 对象发起请求
- 支持两种调用方式：
  1. await-to-js 风格：`const [res, err] = await http.get('/url')`
  2. 直接 await + unwrap：`const res = await http.get('/url').unwrap()`

```typescript
// API 定义示例
import http from '@/http/requestWrapper'
import type { Goods, PaginationParams } from '@/types'

// 获取商品列表
export function getGoodsList(params: PaginationParams) {
  return http.get<{ list: Goods[]; total: number }>('/goods/list', { query: params })
}

// 获取商品详情
export function getGoodsDetail(id: string | number) {
  return http.get<Goods>(`/goods/detail/${id}`)
}
```

## HTTP 请求规范

- 使用 [src/http/requestWrapper.ts](mdc:src/http/requestWrapper.ts) 中封装的请求方法
- 所有请求应处理错误情况，推荐使用 await-to-js 风格 `[res, err] = await http.get()` 或 unwrap 抛出异常
- 支持 GET、POST、PUT、DELETE 四种请求方法
- 请求拦截器和响应拦截器在 [src/http/httpClient.ts](mdc:src/http/httpClient.ts) 和 [src/interceptors/](mdc:src/interceptors/) 目录下实现
- 响应数据结构统一为 `{ code, msg/message, data }`
- 401/登录失效自动处理，token 过期自动刷新，失败自动跳转登录

## 数据类型定义

- 在 `src/types/` 目录下定义接口相关的类型
- API 请求参数和响应数据必须有明确的类型定义
- 相关的类型定义应放在同一文件中，按模块组织

```typescript
// 类型定义示例 (src/types/goods.ts)
export interface Goods {
  id: string | number
  title: string
  price: number
  image: string
  description?: string
  sold: number
  category: string
  tags?: string[]
}

export interface GoodsListParams extends PaginationParams {
  category?: string
  keyword?: string
  sortBy?: 'price' | 'sold' | 'newest'
  order?: 'asc' | 'desc'
}
```

## 典型用法

```typescript
// await-to-js 风格
const [res, err, code, msg] = await http.get('/api/path')
if (err) {
  // 错误处理
}

// unwrap 风格
try {
  const res = await http.get('/api/path').unwrap()
  // 直接拿到 data
} catch (err) {
  // 错误处理
}
```

  sortBy?: 'price' | 'sold' | 'newest'
  order?: 'asc' | 'desc'
}
```
