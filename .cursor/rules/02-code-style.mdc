---
description: 
globs: src/**/*.{vue,ts,js,scss}
alwaysApply: false
---
---
description: 项目代码风格与命名规范指南
applicable_paths: ["src/**/*.{vue,ts,js,scss}"]
---

# 代码风格规范

## Vue 组件规范

- 使用 Vue 3 组合式 API（`<script setup>` 语法）
- 每个组件必须有明确的 `name` 属性（使用 `defineOptions` 或组件选项）
- 组件命名采用 PascalCase 方式（如：`GoodsItem`）
- 文件命名采用 kebab-case 方式（如：`goods-item.vue`）

### 组件结构顺序
```vue
<script lang="ts">
// 普通脚本区域（选项式API声明）
</script>

<script setup lang="ts">
// 组合式API区域
</script>

<template>
  <!-- 模板区域 -->
</template>

<style scoped>
/* 样式区域 */
</style>
```

## TypeScript 规范

- 必须为所有属性（props）、变量和函数参数定义类型
- 使用接口（interface）定义复杂的数据结构
- 避免使用 `any` 类型
- 利用 TypeScript 的类型推导减少冗余声明

## 样式规范

- 优先使用 UnoCSS 原子类
- 所有组件样式必须添加 `scoped` 属性
- 主题色值和通用样式在 `src/uni.scss` 中定义
- 响应式设计以 rpx 为单位

## 命名规范

- 变量和函数使用 camelCase（如：`getUserInfo`）
- 常量使用 UPPER_SNAKE_CASE（如：`MAX_COUNT`）
- 组件使用 PascalCase（如：`UserProfile`）
- 文件使用 kebab-case（如：`user-profile.vue`）
