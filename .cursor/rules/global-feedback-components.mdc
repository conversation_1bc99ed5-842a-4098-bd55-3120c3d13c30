# 全局反馈组件使用规范

## 概述

项目已经实现了全局反馈组件系统，包括：

- `GlobalToast` - 全局提示组件
- `GlobalMessage` - 全局消息组件  
- `GlobalLoading` - 全局加载组件

## 使用规范

### 1. 优先使用全局反馈组件

**✅ 推荐使用：**

```typescript
// 使用Utils工具类统一调用
import Utils from '@/utils/global'

// Toast提示
Utils.toast('简单提示')
Utils.toast({ type: 'success', msg: '操作成功' })
Utils.toast({ type: 'error', msg: '操作失败' })
Utils.toast({ type: 'warning', msg: '警告信息' })
Utils.toast({ type: 'info', msg: '提示信息' })

// Message弹窗
Utils.message('简单提示')
Utils.message({ type: 'alert', title: '提示', message: '操作完成' })
Utils.message({ type: 'confirm', title: '确认', message: '确定删除吗？' })
Utils.message({ type: 'prompt', title: '输入', message: '请输入名称' })

// Loading加载
Utils.loading('加载中...')
Utils.loading({ msg: '处理中...', cover: true })
Utils.closeLoading() // 关闭loading
```

**❌ 避免使用：**

```typescript
// 不要使用uni原生方法
uni.showToast({ title: '提示' })
uni.showModal({ title: '标题', content: '内容' })
uni.showLoading({ title: '加载中' })
```

### 2. 组件位置

全局反馈组件已注册在以下布局文件中：

- [src/layouts/default.vue](mdc:src/layouts/default.vue) - 默认布局
- [src/layouts/full.vue](mdc:src/layouts/full.vue) - 全屏布局
- [src/layouts/theme.vue](mdc:src/layouts/theme.vue) - 主题布局

组件实现文件：

- [src/components/GlobalToast.vue](mdc:src/components/GlobalToast.vue)
- [src/components/GlobalMessage.vue](mdc:src/components/GlobalMessage.vue)
- [src/components/GlobalLoading.vue](mdc:src/components/GlobalLoading.vue)

### 3. 工具类使用

项目提供了统一的Utils工具类来管理全局反馈组件：

- [src/utils/global.ts](mdc:src/utils/global.ts) - 全局工具类，包含toast、message、loading方法
- [src/utils/index.ts](mdc:src/utils/index.ts) - 工具类统一导出

相关的Hook文件（Utils内部使用）：

- [src/hooks/useGlobalToast.ts](mdc:src/hooks/useGlobalToast.ts)
- [src/hooks/useGlobalMessage.ts](mdc:src/hooks/useGlobalMessage.ts)
- [src/hooks/useGlobalLoading.ts](mdc:src/hooks/useGlobalLoading.ts)

#### Utils.toast() 调用方式

```typescript
import Utils from '@/utils/global'

// 简单字符串调用
Utils.toast('提示信息')

// 对象配置调用
Utils.toast({ type: 'success', msg: '操作成功' })
Utils.toast({ type: 'error', msg: '操作失败' })
Utils.toast({ type: 'warning', msg: '警告信息' })
Utils.toast({ type: 'info', msg: '提示信息' })
Utils.toast({ type: 'show', msg: '自定义提示', duration: 3000 })

// 支持的类型：'show' | 'success' | 'error' | 'info' | 'warning'
```

#### Utils.message() 调用方式

```typescript
import Utils from '@/utils/global'

// 简单字符串调用（默认alert类型）
Utils.message('确认信息')

// 对象配置调用
Utils.message({ type: 'alert', title: '提示', message: '操作完成' })
Utils.message({ type: 'confirm', title: '确认', message: '确定删除吗？' })
Utils.message({ type: 'prompt', title: '输入', message: '请输入名称' })

// 支持的类型：'alert' | 'confirm' | 'prompt'
```

#### Utils.loading() 调用方式

```typescript
import Utils from '@/utils/global'

// 显示loading
Utils.loading('加载中...')
Utils.loading({ msg: '处理中...', cover: true })

// 关闭loading
Utils.closeLoading()
```

### 4. 优势

使用全局反馈组件的优势：

- 统一的UI风格和交互体验
- 更好的主题适配
- 可自定义样式和动画
- 更好的国际化支持
- 便于统一管理和维护

### 5. 注意事项

- **优先使用Utils工具类**，而不是直接调用Hook
- Utils提供了统一的API和错误处理机制
- 支持字符串参数和对象参数两种调用方式
- Toast组件会自动关闭，Message组件需要用户交互
- Loading组件需要手动调用`Utils.closeLoading()`关闭
- Utils内部有降级机制，如果全局组件异常会自动降级到原生方法
- 确保在异步操作中正确关闭Loading
- 遵循项目的设计规范和交互模式
- 组件只在当前页面显示，通过页面路径判断
- 支持支付宝小程序的特殊处理（hackAlipayVisible）

### 6. 实际使用示例

```typescript
// 在Vue组件中使用Utils工具类
import Utils from '@/utils/global'

export default {
  setup() {
    const handleSubmit = async () => {
      try {
        Utils.loading('提交中...')
        await submitData()
        Utils.toast({ type: 'success', msg: '提交成功' })
      } catch (error) {
        Utils.toast({ type: 'error', msg: '提交失败' })
      } finally {
        Utils.closeLoading()
      }
    }

    const handleDelete = async () => {
      const result = await Utils.message({ 
        type: 'confirm', 
        title: '确认删除', 
        message: '确定要删除这条记录吗？' 
      })
      if (result?.confirm) {
        // 执行删除操作
        Utils.toast({ type: 'success', msg: '删除成功' })
      }
    }

    const handleSimpleToast = () => {
      // 简单调用
      Utils.toast('操作完成')
    }

    return {
      handleSubmit,
      handleDelete,
      handleSimpleToast
    }
  }
}
```

### 7. 迁移指南

项目中仍有一些文件直接使用uni原生方法，建议逐步迁移：

**需要迁移的文件：**

- [src/components/card-quota.vue](mdc:src/components/card-quota.vue)
- [src/pages/common/login/index.vue](mdc:src/pages/common/login/index.vue)
- [src/pages/user/address-edit.vue](mdc:src/pages/user/address-edit.vue)
- [src/pages/user/settings.vue](mdc:src/pages/user/settings.vue)
- [src/pages/loan/auth.vue](mdc:src/pages/loan/auth.vue)
- [src/pages/shop/order-detail.vue](mdc:src/pages/shop/order-detail.vue)
- [src/http/httpClient.ts](mdc:src/http/httpClient.ts)

**迁移示例：**

```typescript
// 迁移前
uni.showToast({ title: '操作成功', icon: 'success' })
uni.showModal({ title: '确认', content: '确定删除？' })
uni.showLoading({ title: '加载中' })

// 迁移后
Utils.toast({ type: 'success', msg: '操作成功' })
Utils.message({ type: 'confirm', title: '确认', message: '确定删除？' })
Utils.loading('加载中')
```

### 8. 技术细节

**组件特性：**

- 使用Pinia store管理状态
- 支持页面级隔离，只在当前页面显示
- 基于wot-design-uni组件库
- 支持支付宝小程序特殊处理

**降级机制：**

- 当全局组件异常时，自动降级到uni原生方法
- 降级时会打印错误日志
- 确保用户体验不受影响

**性能优化：**

- 使用虚拟主机模式（virtualHost: true）
- 共享样式隔离（styleIsolation: 'shared'）
- 添加全局类名（addGlobalClass: true）

### 9. 最佳实践

1. **统一导入**：使用`import Utils from '@/utils/global'`
2. **错误处理**：在try-catch中使用，确保降级机制生效
3. **Loading管理**：在异步操作中使用finally确保关闭
4. **类型安全**：使用TypeScript类型定义
5. **代码规范**：遵循项目ESLint和Prettier配置

description:
globs:
alwaysApply: false

---
