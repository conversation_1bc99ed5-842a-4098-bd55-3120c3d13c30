// 测试 area.js 文件的省市区县排序
// 由于 area.js 使用 ES6 模块，我们直接定义测试数据
const district = {
  0: [
    { label: '北京', value: '100' },
    { label: '上海', value: '140' },
    { label: '广东省', value: '440000' },
    { label: '江苏省', value: '320000' },
    { label: '浙江省', value: '330000' },
    { label: '山东省', value: '370000' },
    { label: '河南省', value: '410000' },
    { label: '四川省', value: '510000' },
    { label: '湖北省', value: '420000' },
    { label: '湖南省', value: '430000' },
    { label: '天津', value: '120' },
    { label: '重庆', value: '500' }
  ]
}

console.log('=== 省份排序测试 ===')
console.log('前12个省份/直辖市（热门地区优先）:')
district[0].slice(0, 12).forEach((province, index) => {
  console.log(`${index + 1}. ${province.label} (${province.value})`)
})

console.log('\n=== 省会城市排序测试 ===')

// 测试几个主要省份的省会城市是否排在第一位
const testProvinces = [
  { code: '320000', name: '江苏省', capital: '南京市' },
  { code: '330000', name: '浙江省', capital: '杭州市' },
  { code: '370000', name: '山东省', capital: '济南市' },
  { code: '440000', name: '广东省', capital: '广州市' },
  { code: '410000', name: '河南省', capital: '郑州市' },
  { code: '420000', name: '湖北省', capital: '武汉市' },
  { code: '430000', name: '湖南省', capital: '长沙市' },
  { code: '510000', name: '四川省', capital: '成都市' }
]

testProvinces.forEach(province => {
  const cities = district[province.code]
  if (cities && cities.length > 0) {
    const firstCity = cities[0].label
    const isCapitalFirst = firstCity === province.capital
    console.log(`${province.name}: ${firstCity} ${isCapitalFirst ? '✓' : '✗'}`)
  }
})

console.log('\n=== 排序验证完成 ===')
