import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useCommonStore = defineStore('common', () => {
  const systemInfo = ref<any>({})
  const channel = ref(import.meta.env.VITE_APP_DEFAULT_CHANNEL || '') // 渠道号

  const setSystemInfo = (info: any) => {
    systemInfo.value = {
      ...info,
      os: info.osName,
      browser: info.browserName,
      screen: info.screenWidth + 'x' + info.screenHeight,
      device: info.deviceType,
      channel: channel.value
    }
  }

  return { systemInfo, setSystemInfo }
})
