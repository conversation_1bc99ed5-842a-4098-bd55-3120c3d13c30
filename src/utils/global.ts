import { useCommonStore } from '@/store/common'
import { sendTrackingApi } from '@/api/common'
import { useToast } from 'wot-design-uni'
import type { ToastOptions } from 'wot-design-uni/components/wd-toast/types'

// 导入全局组件的 hooks
import { useGlobalToast } from '@/hooks/useGlobalToast'
import { useGlobalMessage } from '@/hooks/useGlobalMessage'
import { useGlobalLoading } from '@/hooks/useGlobalLoading'
import type { GlobalMessageOptions } from '@/hooks/useGlobalMessage'

export type ToastType = 'show' | 'success' | 'error' | 'info' | 'warning' | 'loading'
export type ToastOptionsWithType = ToastOptions & {
  type?: ToastType
}

/**
 * 全局工具类
 */
class Utils {
  /**
   * 深度合并对象
   */
  static deepMerge(target: any, source: any): any {
    if (source === null || typeof source !== 'object') {
      return source
    }
    if (target === null || typeof target !== 'object') {
      return source
    }

    const result = { ...target }
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(target[key], source[key])
        } else {
          result[key] = source[key]
        }
      }
    }
    return result
  }

  /**
   * 判断是否为字符串
   */
  static isString(value: any): value is string {
    return typeof value === 'string'
  }
  /**
   * 系统埋点
   * @param params 埋点参数
   * @returns 埋点数据对象或undefined
   * @example Utils.eventTracking({ eventType: 'page', remark: '进入首页' })
   */
  static eventTracking(params: Partial<API.EventTrackingParams>): Partial<API.EventTrackingParams> | undefined {
    try {
      const commonStore = useCommonStore()
      const { systemInfo } = commonStore
      const now = new Date()

      // 默认参数
      const defaultParams: Partial<API.EventTrackingParams> = {
        ...systemInfo,
        time: now.toISOString(),
        pageUrl: getCurrentPages().length > 0 ? getCurrentPages()[getCurrentPages().length - 1].route : '',
        referer: getCurrentPages().length > 1 ? getCurrentPages()[getCurrentPages().length - 2].route : '',
        eid: params?.eid || params?.remark
      }

      // 合并参数
      const trackingData = { ...defaultParams, ...params }

      // 上报数据
      sendTrackingApi(trackingData as API.EventTrackingParams).catch(err => {
        console.error('埋点上报失败:', err)
      })

      // 开发环境打印埋点数据
      if (import.meta.env.DEV) {
        console.log('埋点数据:', trackingData)
      }

      return trackingData
    } catch (error) {
      console.error('埋点上报异常:', error)
      return undefined
    }
  }

  /**
   * 手机号脱敏中间4位*号
   */
  static phoneMask(phone: string): string {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }

  /**
   * Toast 轻提示 - 使用全局 Toast 组件
   * @example Utils.toast({ type: 'success', msg: '操作成功' })
   * @example Utils.toast({ type: 'error', msg: '操作失败' })
   * @example Utils.toast({ type: 'warning', msg: '警告信息' })
   * @example Utils.toast({ type: 'info', msg: '提示信息' })
   * @example Utils.toast({ type: 'show', msg: '自定义提示' })
   * @example Utils.toast('简单提示') // 默认使用 show 方法
   */
  static toast(options: ToastOptionsWithType | string): void {
    try {
      const globalToast = useGlobalToast()

      if (typeof options === 'string') {
        globalToast.show(options)
        return
      }

      const { type = 'show', ...restOptions } = options

      switch (type) {
        case 'success':
          globalToast.success(restOptions)
          break
        case 'error':
          globalToast.error(restOptions)
          break
        case 'warning':
          globalToast.warning(restOptions)
          break
        case 'info':
          globalToast.info(restOptions)
          break
        case 'show':
        default:
          globalToast.show(restOptions)
          break
      }
    } catch (err) {
      // 降级到原生 Toast
      console.error('全局 Toast 异常，降级到原生 Toast:', err)
      const optionsObj = typeof options === 'object' && options !== null ? options : { msg: options, type: 'show' }
      const msg = typeof optionsObj.msg === 'string' ? optionsObj.msg : '提示'
      switch (optionsObj?.type) {
        case 'success':
          uni.showToast({
            title: msg,
            icon: 'success',
            duration: 2000
          })
          break
        case 'error':
          uni.showToast({
            title: msg,
            icon: 'error',
            duration: 2000
          })
          break
        default:
          uni.showToast({
            title: msg,
            icon: 'none',
            duration: 2000
          })
          break
      }
    }
  }

  /**
   * Message 弹窗 - 使用全局 Message 组件
   * @example Utils.message({ type: 'alert', title: '提示', message: '操作完成' })
   * @example Utils.message({ type: 'confirm', title: '确认', message: '确定删除吗？', success: (res) => {} })
   * @example Utils.message({ type: 'prompt', title: '输入', message: '请输入名称', success: (res) => {} })
   * @example Utils.message('简单提示') // 默认使用 alert 类型
   */
  static message(options: GlobalMessageOptions | string): void {
    try {
      const globalMessage = useGlobalMessage()

      if (typeof options === 'string') {
        globalMessage.alert(options)
        return
      }

      const { type = 'alert', ...restOptions } = options

      switch (type) {
        case 'alert':
          globalMessage.alert(restOptions)
          break
        case 'confirm':
          globalMessage.confirm(restOptions)
          break
        case 'prompt':
          globalMessage.prompt(restOptions)
          break
        default:
          globalMessage.show(restOptions)
          break
      }
    } catch (err) {
      // 降级到原生弹窗
      console.error('全局 Message 异常，降级到原生弹窗:', err)
      const optionsObj = typeof options === 'object' && options !== null ? options : { title: options, type: 'alert' }
      const title = typeof optionsObj.title === 'string' ? optionsObj.title : '提示'
      const content = (optionsObj as any).message || title || ''
      const showCancel = optionsObj.type === 'confirm' || optionsObj.type === 'prompt'
      uni.showModal({
        title,
        content,
        showCancel
      })
    }
  }

  /**
   * Loading 加载 - 使用全局 Loading 组件
   * @example Utils.loading('加载中...')
   * @example Utils.loading({ msg: '处理中...', cover: true })
   * @example Utils.loading.close() // 关闭加载
   */
  static loading(options?: ToastOptions | string): void {
    try {
      const globalLoading = useGlobalLoading()

      if (options === undefined) {
        globalLoading.loading('加载中...')
      } else {
        globalLoading.loading(options)
      }
    } catch (err) {
      // 降级到原生 Loading
      console.error('全局 Loading 异常，降级到原生 Loading:', err)
      const msg = typeof options === 'string' ? options : options?.msg || '加载中...'
      uni.showLoading({
        title: msg,
        mask: true
      })
    }
  }

  /**
   * 关闭 Loading
   */
  static closeLoading(): void {
    try {
      const globalLoading = useGlobalLoading()
      globalLoading.close()
    } catch (err) {
      // 降级到原生方法
      console.error('关闭全局 Loading 异常，降级到原生方法:', err)
      uni.hideLoading()
    }
  }
}

export default Utils
