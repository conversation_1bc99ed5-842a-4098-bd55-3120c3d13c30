<!-- theme.vue -->
<template>
  <WdConfigProvider :theme="theme" :theme-vars="themeVars">
    <!-- <div class="min-h-100vh"> -->
    <slot></slot>
    <wd-toast />
    <wd-message-box />
    <!-- 全局反馈组件 -->
    <GlobalToast />
    <GlobalMessage />
    <GlobalLoading />
    <!-- </div> -->
  </WdConfigProvider>
</template>

<script setup lang="ts">
import { useTheme } from '@/hooks/useTheme'
const { theme, themeVars } = useTheme()
</script>
