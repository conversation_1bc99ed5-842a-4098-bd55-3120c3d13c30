<route lang="json5" type="home">
{
  layout: 'theme', // 使用主题
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '首页'
  }
}
</route>

<template>
  <view class="box-border min-h-screen flex flex-col overflow-hidden bg-[#F3F5F7]">
    <!-- 顶部导航栏 -->
    <view
      class="pos-absolute h-346rpx w-full bg-cover bg-center"
      style="background: url('../../../static/images/home/<USER>') no-repeat top/cover"
    ></view>
    <view class="top-nav my-5 w-full flex items-center px-30rpx">
      <wd-img width="230rpx" mode="widthFix" src="/static/images/home/<USER>" />
      <SearchBox
        :searchApi="searchGoods"
        placeholder="请输入商品名称搜索"
        :debounceMs="400"
        autoSearch
        @select="onSearchSelect"
        @search="onSearch"
      >
        <template #input="{ modelValue, loading, onInput, onSearch, onFocus }">
          <view class="flex items-center w-400rpx m-(l-30rpx r-20rpx) h-50rpx px-24rpx rounded-25rpx bg-white">
            <view class="flex items-center flex-1">
              <span class="i-uiw-search text-20rpx text-[#cfcfcf] mr-2"></span>
              <input
                class="flex-1 text-20rpx bg-transparent outline-none border-none placeholder:text-[#cfcfcf]"
                :value="modelValue"
                :placeholder="'请输入商品名称搜索'"
                @input="e => onInput(e.detail.value)"
                @focus="onFocus"
                @keyup.enter="onSearch"
                style="background: transparent"
              />
            </view>
            <view
              class="ml-2 text-20rpx font-bold"
              style="min-width: 48rpx; color: #f6312d; text-align: right; cursor: pointer"
              @click="onSearch"
            >
              搜索
            </view>
          </view>
        </template>
        <template #results="{ results, select, inputValue, loading }">
          <view class="bg-white rounded-b-8rpx shadow max-h-300rpx overflow-y-auto">
            <view
              v-for="item in results"
              :key="item.id"
              class="px-4 py-2 text-18rpx hover:bg-gray-100 cursor-pointer border-b border-gray-100"
              @click="select(item)"
            >
              <text class="text-primary">{{ item.title || item.name || item.keyword }}</text>
            </view>
            <!-- 无匹配结果展示交由SearchBox组件内部控制 -->
          </view>
        </template>
      </SearchBox>
      <wd-icon name="/static/images/home/<USER>" size="30rpx"></wd-icon>
    </view>

    <!-- 花花卡额度 -->
    <card-quota ref="cardQuotaRef" @data-change="handleCardDataChange" />

    <!-- 调试信息（可选） -->

    <!-- 账单 -->
    <view
      v-if="0"
      class="mx-30rpx my-24rpx box-border h-155rpx flex items-center justify-around rounded-22rpx bg-white pt-15rpx text-center"
    >
      <wd-badge
        lass="flex flex-col items-center h-100rpx"
        :model-value="item.value"
        v-for="item in billList"
        :key="item.key"
      >
        <wd-img width="43rpx" height="43rpx" mode="widthFix" :src="item.icon" />
        <view class="mt-10rpx text-21rpx text-[#575757]">{{ item.name }}</view>
      </wd-badge>
    </view>

    <!-- 功能区九宫格 -->
    <view class="grid grid-cols-5 mt-5 gap-y-4 px-30rpx" v-if="menuList.length > 0">
      <view
        v-for="item in menuList"
        :key="item?.name"
        class="flex flex-col items-center"
        @click="navToSubcategory(item)"
      >
        <view class="mb-10rpx box-border h-82rpx w-82rpx rounded-22rpx bg-white p-13rpx">
          <wd-img width="100%" height="100%" mode="aspectFit" :src="item?.icon" />
        </view>
        <span class="text-22rpx text-[#242424]">{{ item?.name }}</span>
      </view>
    </view>

    <!-- 横幅活动 -->
    <view class="mx-30rpx mt-5" @click="goToActivity(2)">
      <wd-img width="100%" height="180rpx" mode="aspectFit" src="/static/images/home/<USER>" />
    </view>

    <!-- 人气爆款 -->
    <view class="mx-30rpx mt-1.5 rounded-xl">
      <view
        class="h-90rpx flex items-center bg-contain bg-center bg-no-repeat"
        style="background-image: url('/static/images/home/<USER>')"
      >
        <view
          class="ml-auto h-45rpx cursor-pointer pr-20rpx text-20rpx text-white leading-15rpx"
          @click="goToActivity(1)"
        >
          查看更多
          <text class="text-16rpx">▶</text>
        </view>
      </view>

      <view class="mt--10rpx h-220rpx rounded-b-15rpx bg-white" v-if="hotProducts.length > 0">
        <swiper
          class="hot-swipe"
          style="height: 220rpx"
          :autoplay="true"
          :interval="3000"
          :duration="500"
          circular
          :indicator-dots="true"
          indicator-color="#DDDDDD"
          indicator-active-color="#FF4D4F"
        >
          <swiper-item v-for="(page, pageIndex) in hotProducts" :key="pageIndex">
            <view class="flex items-center justify-start">
              <view
                v-for="product in page"
                :key="product.id"
                class="flex flex-col flex-1 items-center max-w-150rpx"
                @click="handleGoodsClick(product)"
              >
                <wd-img width="100rpx" height="100rpx" mode="aspectFit" :src="product?.main_image" class="mb-17rpx" />
                <view class="text-25rpx text-[#FF3C29] font-bold">￥{{ product?.min_price }}</view>
                <view class="text-18rpx text-[#969696]">{{ product?.sales_volume }}+已购</view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>

    <!-- 商品推荐 -->
    <view class="mx-4 mt-5">
      <goods-recommendation ref="goodsRecommendationRef"></goods-recommendation>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CardQuota from '@/components/card-quota.vue'
import SearchBox from '@/components/SearchBox.vue'
import GoodsRecommendation from '@/components/goods-recommendation.vue'
import { ref, onMounted, onUnmounted } from 'vue'
import { getGoodsCategoryListApi, getGoodsSearchApi, getGoodsListApi } from '@/api'
// 必须导入需要用到的页面生命周期（即使在当前页面上没有直接使用到）
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app'

// 组件引用
const cardQuotaRef = ref()

// 卡片数据状态
const cardData = ref({
  amount: '',
  status: '',
  lastUpdateTime: ''
})

// 处理卡片数据变化
const handleCardDataChange = (data: any) => {
  cardData.value = {
    amount: data.amount,
    status: data.cardStatus,
    lastUpdateTime: data.lastUpdateTime
  }
  console.log('卡片数据已更新:', cardData.value)
}

// 手动刷新卡片数据
const refreshCardData = async () => {
  try {
    await cardQuotaRef.value?.refresh()
    uni.showToast({
      title: '数据已刷新',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'none'
    })
  }
}

// 获取当前卡片数据
const getCurrentCardData = () => {
  const data = cardQuotaRef.value?.getData()
  console.log('当前卡片数据:', data)
  return data
}

// 账单列表
const billList = ref([])

// 热门产品轮播图数据
const hotProducts = ref<any[][]>([])

function chunkArray(arr: any[], size: number) {
  const result = []
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size))
  }
  return result
}

// 热门商品轮播
function updateHotProducts() {
  getGoodsListApi({ page: 1, size: 1000, is_hot: 1 })
    .unwrap()
    .then((res: any) => {
      hotProducts.value = chunkArray(res?.data || [], 5)
    })
}
updateHotProducts()

// 菜单列表
const menuList = ref([])

// 获取商品分类列表
getGoodsCategoryListApi({ type: 'home' })
  .unwrap()
  .then(({ data }) => {
    console.log(data)
    menuList.value = data || []
  })

// 商品推荐组件引用
const goodsRecommendationRef = ref()

onPageScroll(e => {
  goodsRecommendationRef.value?.updatePageScrollTop(e.scrollTop)
})
onReachBottom(() => {
  goodsRecommendationRef.value?.pageReachBottom()
})

// 处理商品点击
const handleGoodsClick = (goods: any) => {
  console.log(`goods -->`, goods)

  // 跳转商品详情
  uni.navigateTo({
    url: `/pages/shop/shop-detail?id=${goods.id}`
  })
}

// 跳转到活动页面
const goToActivity = (type: number) => {
  uni.navigateTo({
    url: `/pages/shop/activity?type=${type}`
  })
}

// 跳转到分类页面
const navToSubcategory = (item: any) => {
  console.log(`item -->`, item)
  uni.navigateTo({
    url: `/pages/shop/category?id=${item?.id}`
  })
}

// 搜索API适配器
const searchGoods = async (keyword: string) => {
  if (!keyword) return []
  const res = await getGoodsSearchApi({ keyword, page: 1, size: 10 }).unwrap()
  return res?.data || []
}

// 搜索结果点击/搜索跳转
function onSearchSelect(item: any) {
  uni.navigateTo({ url: `/pages/shop/shop-detail?id=${item.id}` })
}
function onSearch(keyword: string) {
  // 跳转到商品分类列表并带上搜索关键字
  uni.navigateTo({ url: `/pages/shop/category?keyword=${encodeURIComponent(keyword)}` })
}

onShow(() => {
  Utils.eventTracking({ eventType: 'page', remark: '进入首页' })
})
</script>

<style lang="scss" scoped>
.top-nav {
  /* #ifdef APP-PLUS */
  margin-top: calc(var(--status-bar-height));
  /* #endif */
}

:deep(.wd-badge__content) {
  margin-top: -28rpx;
  margin-right: -16rpx;
  transform: scale(0.7) !important;
}
/* 轮播图样式 */
.hot-swipe {
  margin-bottom: 20rpx;
  overflow: hidden;
  border-radius: 12rpx;
}
/* 自定义指示点样式 */
:deep(.uni-swiper-dots-horizontal) {
  bottom: 10rpx;
}

:deep(.uni-swiper-dot) {
  width: 12rpx;
  height: 6rpx !important;
  margin: 0 4rpx;
  background-color: rgb(255 77 79 / 30%) !important;
  border-radius: 6rpx !important;
  transition: all 0.3s;
}

:deep(.uni-swiper-dot-active) {
  width: 24rpx !important;
  background-color: #ff4d4f !important;
}
</style>
