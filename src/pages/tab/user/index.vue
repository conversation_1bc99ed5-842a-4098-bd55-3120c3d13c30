<route lang="json5" type="tab">
{
  layout: 'theme', // 使用主题
  style: {
    navigationStyle: 'custom',
    navigationBarTextStyle: 'white',
    navigationBarTitleText: '我的'
  }
}
</route>

<template>
  <view class="user-page pt-safe">
    <!-- 顶部背景和用户信息 -->
    <view class="header relative">
      <image class="header-bg absolute left-0 top-0 w-full" src="@/static/images/home/<USER>" mode="widthFix" />
      <view class="user-info relative flex items-center justify-between p-40rpx">
        <!-- 左侧用户信息 -->
        <view class="flex items-center">
          <view class="mr-30rpx">
            <image
              class="avatar h-124rpx w-124rpx rounded-full"
              :src="userInfo?.avatar || '/static/images/user/avatar_default.png'"
              mode="aspectFill"
              @click="handleLogin"
            />
          </view>
          <view class="text-white">
            <view v-if="isUserLoggedIn" class="mb-10rpx text-44rpx">{{ Utils.phoneMask(userInfo?.phone) }}</view>
            <view v-else class="text-44rpx" @click="handleLogin">未登录</view>
          </view>
        </view>

        <!-- 右侧设置图标 -->
        <view class="settings-icon" @click="navigateTo('settings')">
          <wd-icon name="setting" size="38rpx" color="#fff" />
        </view>
      </view>
    </view>

    <!-- 订单菜单 -->
    <view
      class="order-section relative mt-[calc(-183rpx-env(safe-area-inset-top))] rounded-20rpx from-[#FFDEDE] to-[#F5F5F5] to-70% bg-gradient-to-b p-(x-30rpx y-22rpx)"
    >
      <view class="mb-30rpx flex items-center justify-between">
        <text class="text-(26rpx #252525) font-500">我的订单</text>
        <view class="flex items-center text-(18rpx #7D7D7D)" @click="navigateTo('orders')">
          <text>查看全部订单</text>
        </view>
      </view>

      <view class="flex justify-around">
        <view
          class="flex flex-col items-center"
          v-for="(item, index) in orderItems"
          :key="index"
          @click="navigateTo('orderType', index)"
        >
          <image
            class="mb-15rpx h-64rpx w-64rpx"
            :class="index === 3 ? 'w-84rpx h-84rpx mb-8rpx mt--13rpx' : index === 0 && 'h-70rpx w-70rpx mb-8rpx'"
            :src="item.icon"
            mode="aspectFit"
          />
          <text class="text-26rpx text-[#333]">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <!-- 花花卡区域 -->
    <view class="card-section mx-30rpx mt-30rpx">
      <view
        class="flower-card relative h-240rpx rounded-16rpx from-[#F2DCCE] to-[#D49D80] bg-gradient-to-r"
        @click="navigateTo('flowerCard')"
      >
        <!-- 卡片内容 -->
        <view class="relative h-full flex justify-between p-30rpx">
          <!-- 左侧信息 -->
          <view class="flex flex-1 flex-col justify-between">
            <!-- 顶部信息 -->
            <view class="border-b-4rpx-#BF8463">
              <view class="mb-10rpx flex items-center">
                <wd-img
                  class="mr-5rpx h-46rpx w-50rpx"
                  src="/static/images/user/card_title_icon.png"
                  mode="heightFix"
                />
                <wd-img class="mr-5rpx mt--10rpx h-44rpx w-108rpx" src="/static/images/user/card_title.png" />
                <text class="ml-10rpx mt-10rpx text-20rpx text-[#A67763]">商城购物先享后付</text>
              </view>
            </view>

            <!-- 底部信息 -->
            <view class="flex flex-auto items-end justify-between text-(left 24rpx #252525)">
              <view class="flex-1">
                <view class="mb-3rpx text-32rpx font-500">¥234</view>
                <view class="text-[#A67763]">可用额度</view>
              </view>
              <view class="flex-1">
                <view class="mb-3rpx font-500">1806.56</view>
                <view class="text-[#A67763]">待还账单</view>
              </view>
            </view>
          </view>

          <!-- 右侧图标 -->
          <view class="mr--50rpx mt--60rpx flex items-center">
            <image class="h-280rpx w-280rpx" src="/static/images/user/card_bg_icon.png" mode="aspectFit" />
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单组 -->
    <view class="menu-section mx-30rpx mb-40rpx mt-30rpx">
      <view class="rounded-20rpx bg-white p-(x-30rpx y-22rpx)">
        <view class="mb-30rpx flex items-center justify-between">
          <text class="text-(26rpx #252525) font-500">其他与服务</text>
        </view>

        <view class="grid grid-cols-4 gap-10rpx">
          <view class="flex flex-col items-center" @click="navigateTo('address')">
            <wd-img class="mb-15rpx mt-8rpx h-48rpx w-48rpx" src="/static/images/user/menu_ads.png" mode="aspectFit" />
            <text class="text-26rpx text-[#333]">地址管理</text>
          </view>

          <view class="flex flex-col items-center" @click="navigateTo('bankCard')">
            <wd-img class="mb-15rpx h-56rpx w-56rpx" src="/static/images/user/menu_band.png" mode="aspectFit" />
            <text class="text-26rpx text-[#333]">银行卡管理</text>
          </view>

          <view class="flex flex-col items-center" @click="navigateTo('message')">
            <wd-img class="mb-15rpx h-56rpx w-56rpx" src="/static/images/user/menu_wx.png" mode="aspectFit" />
            <text class="text-26rpx text-[#333]">客服</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const statusBarHeight = ref(20)
const orderItems = [
  { name: '待付款', icon: '/static/images/user/order_icon_0.png' },
  { name: '待发货', icon: '/static/images/user/order_icon_1.png' },
  { name: '待收货', icon: '/static/images/user/order_icon_2.png' },
  { name: '已完成', icon: '/static/images/user/order_icon_3.png' }
]

// 用户信息
const { userInfo } = storeToRefs(userStore)
const isUserLoggedIn = computed(() => !!userInfo.value?.accessToken)

onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 20
})

// 跳转策略 map
const navigationStrategies: Record<string, (params?: any) => string> = {
  flowerCard: () => '/pages/user/flower-card',
  bankCard: () => '/pages/user/bank-card',
  addCard: () => '/pages/user/bank-card-add',
  address: () => '/pages/user/address-list',
  orders: () => '/pages/shop/order-list',
  orderType: (type: number) => `/pages/shop/order-list?type=${type}`,
  settings: () => '/pages/user/settings',
  collection: () => '/pages/user/collection',
  message: () => '/pages/user/message',
  login: () => '/pages/common/login/index'
}

// 统一跳转函数
function navigateTo(type: string, params?: any) {
  const strategy = navigationStrategies[type]
  if (!strategy) return
  const url = strategy(params)
  uni.navigateTo({ url })
}

// 处理登录点击
const handleLogin = () => {
  if (!isUserLoggedIn.value) {
    navigateTo('login')
  }
}

const toCopy = () => {
  uni.setClipboardData({
    data: '123456',
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.header {
  height: 420rpx;

  .header-bg {
    z-index: 0;
    height: 100%;
  }

  .avatar {
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  }

  .settings-icon {
    padding: 10rpx;
  }
}

:deep(.wd-cell-group) {
  background-color: transparent;
}

:deep(.wd-cell) {
  margin-bottom: 2rpx;
}

.user-info {
  padding-top: calc(var(--window-top) + 80rpx) !important;
}
</style>
