<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '设置',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="setting-container px-20rpx pt-4">
    <!-- 设置选项分组 -->
    <template v-for="(group, groupKey) in groupedSettings" :key="groupKey">
      <view class="setting-section" :class="{ 'mt-4': groupKey !== 'account' }">
        <wd-cell-group>
          <wd-cell v-for="item in group" :key="item.key" :title="item.title" is-link @click="() => navigateTo(item)" />
        </wd-cell-group>
      </view>
    </template>

    <!-- 退出登录按钮 -->
    <view class="logout-section px-4 mt-50vh mb-8">
      <wd-button class="!bg-transparent" type="info" hairline plain size="large" block @click="handleLogout">
        退出登录
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/store/user'
import { logoutApi } from '@/api'

// 组件名称定义
defineOptions({
  name: 'SettingPage'
})

// 用户状态管理
const userStore = useUserStore()

// 设置项策略配置 - 直接在items中定义url
const settingItems = [
  { key: 'accountSecurity', title: '账户与安全', url: '/pages/user/account-security', group: 'account' },
  { key: 'privacy', title: '隐私协议', url: '/pages/user/privacy-policy', group: 'other' },
  { key: 'about', title: '关于我们', url: '/pages/user/about-us', group: 'other' },
  { key: 'afterSales', title: '售后政策', url: '/pages/user/after-sales-policy', group: 'other' }
] as const

// 按group字段分组设置项
const groupedSettings = computed(() => {
  const groups: Record<string, Array<(typeof settingItems)[number]>> = {}
  settingItems.forEach(item => {
    if (!groups[item.group]) {
      groups[item.group] = []
    }
    groups[item.group].push(item)
  })
  return groups
})

// 导航策略函数 - 直接使用item中的url
const navigateTo = (item: (typeof settingItems)[number]) => {
  uni.navigateTo({ url: item.url })
}

// 退出登录
const handleLogout = () => {
  console.log(`1 -->`, 1)
  Utils.message({
    type: 'confirm',
    title: '提示',
    msg: '确定要退出登录吗？',
    success: () => {
      logoutApi()
    },
    fail: () => {
      console.log(`3 -->`, 3)
    }
  })
}
</script>

<style lang="scss" scoped>
.setting-container {
  .setting-section {
    :deep(.wd-cell-group) {
      overflow: hidden;
      border-radius: 12rpx;

      .wd-cell__wrapper {
        padding: var(--wot-cell-wrapper-padding, 30rpx) 0 var(--wot-cell-wrapper-padding, 30rpx) 20rpx;
        border-bottom: 1rpx solid #f5f2f2;
      }
      .wd-cell {
        padding-right: var(--wot-cell-padding, var(--wot-size-side-padding, 15px));
        margin-bottom: 2rpx;
      }

      .wd-cell:last-child {
        .wd-cell__wrapper {
          border-bottom: 1rpx solid transparent;
        }
      }
    }
  }
}
</style>
