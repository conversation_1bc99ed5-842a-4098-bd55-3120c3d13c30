<route lang="json5" type="page">
{
  layout: false,
  style: {
    navigationBarTitleText: '我的地址',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <uni-layout name="full">
    <view class="address-list-page box-border flex flex-col bg-gray-100 px-30rpx">
      <!-- 地址列表 -->
      <view v-if="addressList.length > 0">
        <view v-for="(item, index) in addressList" :key="item.id" class="mt-20rpx">
          <wd-swipe-action>
            <template #right>
              <wd-button
                class="btn-primary !min-w-150rpx !h-full !rounded-none !text-#fff"
                type="danger"
                @click="deleteAddress(item)"
              >
                删除
              </wd-button>
            </template>
            <address-item
              :address="item"
              mode="manage"
              @click="handleAddressClick(item)"
              @edit="editAddress"
            ></address-item>
          </wd-swipe-action>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="flex-col-center flex-1 pt-25vh">
        <wd-status-tip image="content" tip="暂无收货地址" image-size="350rpx" />
      </view>
    </view>

    <template #footer>
      <!-- 底部添加按钮 -->
      <view class="bg-white !px-50rpx pb-[calc(var(--window-bottom)+20rpx)]">
        <wd-button
          class="btn-primary !h-80rpx"
          icon="/static/images/user/add-address-icon.png"
          block
          style="background: linear-gradient(to right, #ff5d5d, #ff3c29)"
          @click="navigateToAddAddress"
        >
          添加收货地址
        </wd-button>
      </view>
    </template>

    <wd-message-box />
  </uni-layout>
</template>

<script lang="ts" setup>
import { getUserAddressListApi, deleteUserAddressApi } from '@/api'
import { useMessage } from 'wot-design-uni'

const message = useMessage()

// 地址列表
const addressList = ref([])

// 获取地址列表方法
function fetchAddressList() {
  getUserAddressListApi()
    .unwrap()
    .then(res => {
      addressList.value = res.data.addresses
    })
}

// 当前选中的地址（用于删除操作）
const currentAddress = ref(null)

// 从哪个页面跳转过来
const fromPage = ref('')

// 页面加载
onLoad(options => {
  if (options.from) {
    fromPage.value = options.from
  }
  // 监听刷新事件
  uni.$on('refreshAddressList', fetchAddressList)
})

// 点击地址
const handleAddressClick = address => {
  // 如果是从订单页跳转过来，则选择地址后返回
  if (fromPage.value === 'order') {
    // 通过事件总线传值
    uni.$emit('selectAddress', address)

    // 返回上一页
    uni.navigateBack()
  }
}

// 编辑地址
const editAddress = address => {
  uni.navigateTo({
    url: `/pages/user/address-edit?id=${address.id}&from=${fromPage.value}`
  })
}

// 删除地址
const deleteAddress = address => {
  console.log(`1 -->`, address)
  currentAddress.value = address

  message
    .confirm({
      title: '删除地址',
      msg: '确定要删除该地址吗？',
      zIndex: 1000
    })
    .then(res => {
      confirmDelete()
    })
}

// 确认删除
const confirmDelete = () => {
  if (!currentAddress.value) return

  deleteUserAddressApi(currentAddress.value.id)
    .unwrap()
    .then(res => {
      const index = addressList.value.findIndex(item => item.id === currentAddress.value.id)
      if (index !== -1) {
        addressList.value.splice(index, 1)
        Utils.toast({ type: 'success', msg: res.message })
      }

      currentAddress.value = null
    })
    .catch(err => {
      Utils.toast({ type: 'error', msg: err.data.message })
    })
}

// 跳转到添加地址页面
const navigateToAddAddress = () => {
  uni.navigateTo({
    url: '/pages/user/address-edit'
  })
}

// 每次显示页面时更新地址列表
onShow(() => {
  fetchAddressList()
})
// 页面卸载时解绑事件
onUnload(() => {
  uni.$off('refreshAddressList', fetchAddressList)
})
</script>

<style>
.flex-col-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
