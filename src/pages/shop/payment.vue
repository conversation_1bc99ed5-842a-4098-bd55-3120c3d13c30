<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '确认支付',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="payment-page pt-30rpx text-#222">
    <!-- 商品信息 -->
    <view class="product-info m-30rpx mt-0 flex-col-center rounded-12rpx bg-white py-20rpx">
      <view class="product-title mb-30rpx text-24rpx">商品总额</view>
      <view class="price-amount mb-26rpx text-44rpx font-bold">¥{{ amount }}</view>
      <view class="countdown text-24rpx">支付剩余时间：{{ countdownMinutes }}:{{ countdownSeconds }}</view>
    </view>

    <!-- 花花卡额度支付 -->
    <view class="payment-options m-30rpx rounded-12rpx bg-white p-22rpx">
      <wd-img src="/static/images/shop/pay-title.png" height="88rpx" mode="heightFix" />
      <view class="option-subtitle mb-30rpx mt--40rpx text-18rpx">分期额度￥1431</view>
      <!-- 分期 -->
      <view class="payment-method-row mb-50rpx h-66rpx w-full flex overflow-x-auto lh-66rpx">
        <view
          v-for="plan in installmentPlans"
          :key="plan.id"
          class="mr-20rpx cursor-pointer rounded-12rpx bg-#F5F5F5 px-20rpx text-(nowrap 18rpx)"
          :class="{ 'text-red-500 bg-red-50 border-red-500': selectedPlan.id === plan.id }"
          @click="selectInstallmentPlan(plan)"
        >
          <span>{{ plan.label }}</span>
        </view>
      </view>

      <!-- 还款计划按钮 -->
      <view class="option-row flex cursor-pointer justify-between text-24rpx" @click="showRepaymentDialog = true">
        <view class="label text-#222">还款计划</view>
        <view class="value flex items-center">
          <wd-icon name="arrow-right" size="34rpx" class="ml-10rpx text-#D3D3D3"></wd-icon>
        </view>
      </view>

      <view class="payment-divider my-20rpx h-2rpx bg-gray-200"></view>

      <!-- 放款机构和金融机构 -->
      <view class="option-row flex justify-between py-10rpx text-24rpx text-#222">
        <view class="label">放款机构</view>
        <view class="value">金融机构</view>
      </view>
    </view>

    <!-- 其他支付方式 -->
    <wd-cell
      class="m-30rpx rounded-12rpx bg-white py-10rpx"
      title="其他支付方式"
      is-link
      :value="selectedPaymentMethod.name"
      @click="showPaymentMethodDialog = true"
    >
      <template #value>
        <text v-if="selectedPaymentMethod.name">{{ selectedPaymentMethod.name }}</text>
      </template>
      <template #right-icon>
        <wd-icon name="arrow-right" size="34rpx" class="text-#D3D3D3"></wd-icon>
      </template>
    </wd-cell>

    <!-- 底部操作栏：协议和支付按钮 -->
    <view class="submit-section fixed bottom-0 left-0 w-full bg-white px-30rpx py-20rpx shadow-sm">
      <!-- 同意协议 -->
      <view class="mb-20rpx">
        <label class="flex cursor-pointer items-center text-24rpx">
          <radio
            :checked="agreeTerms"
            :color="'#FF4D4F'"
            class="mr-10rpx scale-80 transform"
            @click="agreeTerms = !agreeTerms"
          />
          <text class="text-#666">
            我已阅读并同意
            <text class="text-#FF4D4F">《支付协议》</text>
          </text>
        </label>
      </view>

      <!-- 立即支付按钮 -->
      <wd-button
        custom-class="!h-82rpx !text-28rpx"
        :class="canSubmit ? 'btn-primary' : 'btn-primary-disabled'"
        :disabled="!canSubmit"
        @click="confirmPayment"
      >
        立即支付
      </wd-button>
    </view>

    <!-- 还款计划弹窗 -->
    <wd-popup
      v-model="showRepaymentDialog"
      position="bottom"
      closable
      custom-class="p-(x-56rpx y-46rpx)"
      :z-index="999"
    >
      <repayment-plan
        :plan-details="repaymentPlanDetails"
        :total-amount="totalRepaymentAmount"
        :annual-rate="annualRate"
        max-height="70vh"
      />
    </wd-popup>

    <!-- 支付方式弹窗 -->
    <wd-popup
      v-model="showPaymentMethodDialog"
      position="bottom"
      closable
      :close-on-click-modal="true"
      custom-class="py-46rpx"
    >
      <view class="payment-method-popup">
        <!-- 标题 -->
        <view class="mb-30rpx flex-1 text-(center 36rpx) font-500">支付方式</view>

        <!-- 支付方式列表 -->
        <view class="payment-methods mb-30rpx">
          <wd-cell-group border>
            <wd-checkbox-group v-model="selectedPaymentMethodId" size="large" checked-color="var(--primary-color)">
              <wd-cell
                v-for="(method, index) in paymentMethods"
                :key="method.id"
                center
                clickable
                @click="selectPaymentMethod(method)"
              >
                <template #icon>
                  <wd-icon
                    :name="`/static/images/shop/payment-method-${index + 1}.png`"
                    size="52rpx"
                    class="mr-16rpx"
                  ></wd-icon>
                </template>

                <template #title>
                  <view class="h-52rpx text-24rpx font-500 leading-52rpx">{{ method.name }}</view>
                </template>
                <wd-checkbox v-model="method.id" :ref="`checkBox${index}`" custom-style="margin:0;"></wd-checkbox>
              </wd-cell>
            </wd-checkbox-group>
          </wd-cell-group>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import RepaymentPlan from '@/components/repayment-plan.vue'

// 声明全局对象
declare const uni: any

// 定义类型
interface InstallmentPlan {
  id: number
  label: string
  periods: number
  amount: number
}

interface PaymentMethod {
  id: number
  name: string
}

// 状态定义
const amount = ref(7709.0)
const countdownMinutes = ref('00')
const countdownSeconds = ref('59')
const agreeTerms = ref(false)
const showRepaymentDialog = ref(false)
const showPaymentMethodDialog = ref(false)
const selectedPlan = ref<InstallmentPlan | Record<string, never>>({})
const selectedPaymentMethodId = ref<number[]>([])

// 数据定义
const installmentPlans: InstallmentPlan[] = [
  { id: 1, label: '¥7709.00×1期 (30天)', periods: 1, amount: 7709.0 },
  { id: 2, label: '¥2569.67×3期 (30天)', periods: 3, amount: 2569.67 },
  { id: 3, label: '¥1284.83×6期 (30天)', periods: 6, amount: 1284.83 }
]

const paymentMethods: PaymentMethod[] = [
  { id: 1, name: '银行卡支付' },
  { id: 2, name: '分期支付' },
  { id: 3, name: '组合支付' }
]

const annualRate = 24
const repaymentDay = 2

// 计算属性
const canSubmit = computed(() => {
  return Object.keys(selectedPlan.value).length > 0 && agreeTerms.value
})

const totalRepaymentAmount = computed(() => {
  if (!selectedPlan.value.id) return amount.value
  return (selectedPlan.value as InstallmentPlan).amount * (selectedPlan.value as InstallmentPlan).periods
})

const selectedPaymentMethod = computed<PaymentMethod | Record<string, never>>(() => {
  if (!selectedPaymentMethodId.value.length) return {}
  return paymentMethods.find(method => selectedPaymentMethodId.value.includes(method.id)) || {}
})

const repaymentPlanDetails = computed(() => {
  if (!selectedPlan.value.id) return []

  const items = []
  const today = new Date()
  const monthlyAmount = (selectedPlan.value as InstallmentPlan).amount
  const principal = 300.0
  const interest = 63.0

  for (let i = 0; i < (selectedPlan.value as InstallmentPlan).periods; i++) {
    items.push({
      date: `${today.getFullYear().toString().slice(2, 4)}/${(today.getMonth() + 2).toString().padStart(2, '0')}/${repaymentDay.toString().padStart(2, '0')}`,
      amount: monthlyAmount,
      principal: principal,
      interest: interest
    })
  }

  return items
})

// 生命周期钩子
onMounted(() => {
  // 默认选择第二个分期计划
  selectInstallmentPlan(installmentPlans[1])
  // 启动倒计时
  startCountdown()
})

// 方法定义
const goBack = () => {
  uni.navigateBack()
}

const selectInstallmentPlan = (plan: InstallmentPlan) => {
  selectedPlan.value = plan
}

const selectPaymentMethod = (method: PaymentMethod) => {
  selectedPaymentMethodId.value = [method.id]
  showPaymentMethodDialog.value = false
}

const startCountdown = () => {
  let totalSeconds = 60 * 30 // 30分钟倒计时

  const updateCountdown = () => {
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = totalSeconds % 60

    countdownMinutes.value = minutes.toString().padStart(2, '0')
    countdownSeconds.value = seconds.toString().padStart(2, '0')

    if (totalSeconds > 0) {
      totalSeconds--
      setTimeout(updateCountdown, 1000)
    } else {
      // 倒计时结束，可以执行相关操作
      uni.showToast({
        title: '支付超时',
        icon: 'none'
      })
    }
  }

  updateCountdown()
}

const confirmPayment = () => {
  if (!canSubmit.value) return

  // 这里添加支付逻辑
  uni.showToast({
    title: '支付请求已提交',
    icon: 'success'
  })

  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/shop/payment-result'
    })
  }, 1000)
}
</script>

<style lang="scss" scoped>
.payment-method-row {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  & > :last-child {
    margin-right: 0 !important;
  }

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }
}

.plan-item:last-child {
  border-bottom: none;
}
</style>
