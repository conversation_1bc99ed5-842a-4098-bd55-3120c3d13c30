<route lang="json5">
{
  layout: false,
  style: {
    navigationBarTitleText: '订单详情',
    navigationStyle: 'custom',
    customNav: true
  }
}
</route>

<template>
  <uni-layout name="full">
    <template #header-bg>
      <view class="header-bg"></view>
    </template>

    <view class="main">
      <!-- 订单状态区域 -->
      <view class="status-card pl-6rpx">
        <view class="flex items-center">
          <wd-img
            src="/static/images/shop/order_detail_icon.png"
            class="status-icon w-72rpx h-72rpx mr-10rpx"
            mode="aspectFit"
          />
          <view class="text-40rpx font-700">{{ getStatusText(orderInfo.status) }}</view>
        </view>

        <!-- 待付款状态显示倒计时 -->
        <view
          v-if="orderInfo.status === 'pending'"
          class="inline-block text-24rpx bg-#FCF9E8 rounded-full px-20rpx py-8rpx text-#D4786E mt-10rpx"
        >
          支付剩余时间：{{ orderInfo.countDown }}
        </view>
      </view>

      <!-- 收货地址 -->
      <address-item :address="orderAddress" mode="select" />

      <!-- 商品信息 -->
      <view class="goods-card bg-white mt-20rpx p-30rpx">
        <view class="goods-info flex">
          <image
            class="goods-image h-160rpx w-160rpx flex-shrink-0 rounded-8rpx"
            :src="orderInfo.goodsImage"
            mode="aspectFill"
          />
          <view class="goods-detail ml-20rpx flex-1 flex flex-col justify-between">
            <view>
              <view class="goods-name line-clamp-2 text-28rpx font-medium">{{ orderInfo.goodsName }}</view>
              <view
                class="goods-spec inline-block h-34rpx rounded-8rpx bg-#F2F2F2 px-10rpx text-center text-20rpx lh-34rpx"
              >
                {{ orderInfo.goodsSpecs }}
              </view>
            </view>
            <view class="mt-16rpx flex justify-between items-center">
              <text class="price text-primary text-28rpx font-bold">¥{{ orderInfo.goodsPrice }}</text>
              <text class="text-22rpx">x{{ orderInfo.goodsCount }}</text>
            </view>
          </view>
        </view>
        <view class="delivery-info mt-20rpx flex justify-between items-center border-t-1rpx-#F2F2F2 pt-20rpx">
          <text class="text-22rpx">配送方式</text>
          <text class="text-22rpx">顺丰快递</text>
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="order-info bg-white mt-20rpx p-30rpx text-24rpx">
        <view class="info-item flex item-center justify-between py-15rpx">
          <text class="">订单编号</text>
          <view class="flex-center text-#8A8A8A">
            <text>{{ orderInfo.orderNo }}</text>
            <text class="inline-block h-50% w-2rpx mx-15rpx bg-gray-300"></text>
            <text class="text-#000" @click="copyOrderId">复制</text>
          </view>
        </view>
        <view class="info-item flex justify-between py-15rpx">
          <text class="">创建时间</text>
          <text class="text-#8A8A8A">{{ orderInfo.orderTime }}</text>
        </view>
        <view class="info-item flex justify-between py-15rpx">
          <text class="">商品金额</text>
          <text class="">¥{{ orderInfo.goodsPrice }}</text>
        </view>
        <view class="info-item flex justify-between py-15rpx">
          <text class=" ">运费</text>
          <text class="">包邮</text>
        </view>
        <view class="info-item flex h-50rpx lh-50rpx justify-end border-t-1rpx-#EDEDED">
          <text class="">实际付款:&nbsp;</text>
          <text class="text-28rpx text-primary font-bold">¥{{ orderInfo.totalPrice }}</text>
        </view>
      </view>

      <!-- 还款计划 -->
      <view
        class="repayment-entry bg-white flex items-center justify-between px-30rpx h-88rpx mt-20rpx rounded-20rpx cursor-pointer"
        @click="showRepaymentDialog = true"
      >
        <view class="text-28rpx text-#222">还款计划</view>
        <view class="flex items-center">
          <view class="text-28rpx text-#666 mr-10rpx">查看</view>
          <wd-icon name="arrow-right" size="32rpx" color="#999" />
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <template #footer>
      <view class="footer-actions bg-white w-full flex">
        <template v-if="orderInfo.status === 'pending'">
          <view class="flex-1 px-30rpx py-20rpx">
            <wd-button class="btn-primary-outline w-full" @click="cancelOrder">取消订单</wd-button>
          </view>
          <view class="flex-1 px-30rpx py-20rpx">
            <wd-button class="btn-primary w-full" @click="payOrder">去支付</wd-button>
          </view>
        </template>
        <template v-else-if="orderInfo.status === 'processing'">
          <view class="flex-1 px-30rpx py-20rpx">
            <wd-button custom-class="btn-primary-outline w-full" @click="cancelOrder">取消订单</wd-button>
          </view>
        </template>
        <template v-else-if="orderInfo.status === 'shipped'">
          <view class="flex-1 px-30rpx py-20rpx">
            <wd-button class="btn-primary-outline w-full" @click="viewLogistics">查看物流</wd-button>
          </view>
          <view class="flex-1 px-30rpx py-20rpx">
            <wd-button class="btn-primary w-full" @click="confirmReceive">确认收货</wd-button>
          </view>
        </template>
        <template v-else>
          <view class="flex-1 px-30rpx py-20rpx">
            <wd-button class="btn-primary-outline w-full" @click="deleteOrder">删除订单</wd-button>
          </view>
          <view class="flex-1 px-30rpx py-20rpx">
            <wd-button class="btn-primary w-full" @click="reorder">再来一单</wd-button>
          </view>
        </template>
      </view>

      <!-- 还款计划弹窗 -->
      <wd-popup
        v-model="showRepaymentDialog"
        position="bottom"
        closable
        custom-class="p-(x-56rpx y-46rpx)"
        :z-index="999"
      >
        <repayment-plan
          :plan-details="repaymentPlanDetails"
          :total-amount="totalRepaymentAmount"
          :annual-rate="annualRate"
          max-height="70vh"
        />
      </wd-popup>
    </template>
  </uni-layout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import AddressItem from '@/components/address/address-item.vue'
import RepaymentPlan from '@/components/repayment-plan.vue'

// 接收路由参数
const orderInfo = ref<any>({
  id: '',
  orderNo: '1000012345623',
  orderTime: '2025-10-30 12:00',
  status: 'pending',
  paymentMethod: '支付宝',
  goodsName: '商品名称商品名称商品名称商品名称',
  goodsImage: '/static/images/home/<USER>',
  goodsSpecs: '黑 12+256G',
  goodsPrice: '7709',
  goodsCount: 1,
  totalPrice: '7709',
  countDown: '00:29:59',
  goodsId: '10001',
  contactName: '张先生',
  contactPhone: '17789898989',
  address: '中关村智慧人才与产业创新基地'
})

// 构造 orderAddress 对象，适配 address-item 组件
const orderAddress = computed(() => ({
  name: orderInfo.value.contactName,
  phone: orderInfo.value.contactPhone,
  fullAddress: orderInfo.value.address
}))

// 获取订单状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待付款',
    processing: '待发货',
    shipped: '待收货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取订单状态描述
const getStatusDescription = (status: string) => {
  const descMap: Record<string, string> = {
    pending: '请在规定时间内完成支付，超时订单将自动取消',
    processing: '您的包裹已准备就绪，等待商家发货',
    shipped: '商品已发出，请注意查收',
    completed: '订单已完成，感谢您的购买',
    cancelled: '订单已取消'
  }
  return descMap[status] || ''
}

// 加载订单详情
const loadOrderDetail = (id: string) => {
  // 这里应该是调用API获取订单详情
  // 模拟加载
  console.log('加载订单ID:', id)
  // 实际项目中应该通过API获取订单详情
}

// 按钮事件处理
const cancelOrder = () => {
  uni.showModal({
    title: '提示',
    content: '确定要取消该订单吗？',
    success: res => {
      if (res.confirm) {
        uni.showToast({
          title: '订单已取消',
          icon: 'success'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}

const payOrder = () => {
  uni.navigateTo({
    url: `/pages/shop/payment?orderNo=${orderInfo.value.orderNo}&amount=${orderInfo.value.totalPrice}`
  })
}

const viewLogistics = () => {
  uni.showToast({
    title: '查看物流功能开发中',
    icon: 'none'
  })
}

const confirmReceive = () => {
  uni.showModal({
    title: '提示',
    content: '确认已收到商品吗？',
    success: res => {
      if (res.confirm) {
        uni.showToast({
          title: '确认收货成功',
          icon: 'success'
        })
        // 修改订单状态
        orderInfo.value.status = 'completed'
      }
    }
  })
}

const deleteOrder = () => {
  uni.showModal({
    title: '提示',
    content: '确定要删除该订单吗？',
    success: res => {
      if (res.confirm) {
        uni.showToast({
          title: '订单已删除',
          icon: 'success'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}

const reorder = () => {
  // 直接跳转到确认订单页面
  uni.navigateTo({
    url: `/pages/shop/order-confirm?goodsId=${orderInfo.value.goodsId || ''}&goodsCount=${orderInfo.value.goodsCount}&isReorder=true&orderId=${orderInfo.value.id}`
  })
}

// 复制订单号
const copyOrderId = () => {
  uni.setClipboardData({
    data: orderInfo.value.orderNo,
    success: () => {
      uni.showToast({
        title: '订单号已复制',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none'
      })
    }
  })
}

const goBack = () => {
  uni.navigateBack()
}

const showRepaymentDialog = ref(false)
// mock数据
const annualRate = 24
const totalRepaymentAmount = 8000
const repaymentPlanDetails = [
  { date: '24/08/02', amount: 2700, principal: 2500, interest: 200 },
  { date: '24/09/02', amount: 2700, principal: 2500, interest: 200 },
  { date: '24/10/02', amount: 2600, principal: 2500, interest: 100 }
]

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-ignore
  const options = currentPage.options
  if (options && options.id) {
    orderInfo.value.id = options.id
    orderInfo.value.orderNo = options.orderNo || orderInfo.value.orderNo
    // 加载订单详情
    loadOrderDetail(options.id)
  }
})
</script>

<style lang="scss" scoped>
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 624rpx;
  background-image: url('/static/images/login/bg.png');
  background-repeat: no-repeat;
  background-size: cover;
}

.main {
  position: relative;
  z-index: 1;
  padding: 30rpx;
  padding-bottom: env(safe-area-inset-bottom);
  & > * {
    margin-bottom: 24rpx;
    border-radius: 20rpx;
  }
}

.btn-primary,
.btn-primary-outline {
  @apply text-white bg-primary rounded-full h-80rpx leading-80rpx text-28rpx;
  &:active {
    opacity: 0.8;
  }
}

.btn-primary-outline {
  @apply rounded-full  !bg-#F0F0F0 !text-gray-700 h-80rpx leading-80rpx text-28rpx;
  &:active {
    opacity: 0.8;
  }
}
</style>
