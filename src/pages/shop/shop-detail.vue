<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '商品详情',
    navigationStyle: 'custom'
  }
}
</route>

<template>
  <view class="shop-detail min-h-100vh flex flex-col bg-white">
    <!-- 返回按钮 -->
    <view class="fixed left-20rpx top-50rpx z-100 h-64rpx w-64rpx flex items-center justify-center" @click="handleBack">
      <wd-icon name="thin-arrow-left" size="30rpx"></wd-icon>
    </view>

    <!-- 轮播图 -->
    <view class="w-full" style="position: relative; height: 800rpx">
      <swiper
        class="h-full w-full"
        :indicator-dots="false"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        circular
        @change="handleSwiperChange"
      >
        <swiper-item class="flex-center" v-for="(item, index) in bannerList" :key="index">
          <image :src="item" class="h-683rpx w-full" mode="aspectFit" />
        </swiper-item>
      </swiper>
      <view
        class="absolute bottom-20rpx right-30rpx rounded-20rpx bg-black bg-opacity-40 px-15rpx py-6rpx text-24rpx text-white"
      >
        {{ currentBanner + 1 }}/{{ bannerList.length }}
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="product-info p-30rpx">
      <view class="flex items-baseline justify-between">
        <!-- 金额 -->
        <view class="flex items-baseline text-[var(--primary-color)]">
          <text class="text-25rpx">¥</text>
          <text class="text-44rpx font-bold">{{ (productInfo.min_price ?? 0).toFixed(2) }}</text>
        </view>
        <view class="text-24rpx text-[#999]">销量 {{ productInfo.sales_volume ?? 0 }}</view>
      </view>
      <view class="mt-20rpx text-32rpx text-[#333] font-medium">
        {{ productInfo.name + ' ' + productInfo.description }}
      </view>
      <view class="mt-40rpx flex items-center text-25rpx text-[#999]">
        <text>库存 {{ productInfo.stock }}</text>
        <view class="inline-block" v-for="(item, index) in productInfo.tags" :key="item.id">
          <text class="mx-20rpx" v-if="item">|</text>
          <text>{{ item }}</text>
        </view>
      </view>
    </view>

    <view class="h-20rpx bg-gray-100"></view>

    <!-- 商品规格 -->
    <view class="product-spec p-30rpx">
      <view
        v-for="(item, index) in productSpecs"
        :key="index"
        class="flex items-center py-15rpx"
        :class="{ 'border-b border-gray-100': index < productSpecs.length - 1 }"
        @click="item.clickable && openSpecPopup()"
      >
        <view class="w-200rpx">
          <text class="text-28rpx text-[#333]">{{ item.label }}</text>
        </view>
        <view class="flex flex-1 items-center justify-between">
          <text class="text-26rpx text-[#666]">{{ item.value }}</text>
          <wd-icon v-if="item.showArrow" name="arrow-right" size="32rpx" color="#999" />
        </view>
      </view>
    </view>

    <view class="h-30rpx bg-gray-100"></view>

    <!-- 商品详情 -->
    <view class="product-detail">
      <!--       
       <view class="pb-20rpx border-b border-gray-200">
        <text class="text-32rpx font-medium text-[#333]">商品详情</text>
      </view> 
      -->
      <view class="rich-text-container">
        <wd-img
          v-for="img in productInfo.detail_image"
          :key="img"
          :src="img"
          mode="widthFix"
          class="w-full mt--10rpx last:mb-150rpx"
        />
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <wd-popup v-model="showSpecPopup" position="bottom" round :z-index="100">
      <view class="spec-popup p-30rpx pb-30rpx">
        <view class="flex items-start pb-20rpx">
          <view
            class="relative overflow-hidden border border-gray-200 rounded-8rpx"
            style="width: 100rpx; height: 100rpx"
          >
            <image :src="selectedSku?.image" class="h-full w-full" mode="aspectFill" />
          </view>
          <view class="ml-20rpx flex-1">
            <view class="text-32rpx text-[#FF0000] font-bold">¥ {{ selectedSku?.price }}</view>
            <view class="mt-10rpx text-24rpx text-[#666]">已选: {{ selectedSku?.name || '请选择规格' }}</view>
          </view>
          <view class="p-10rpx" @click="showSpecPopup = false">
            <wd-icon name="close" size="32rpx" color="#999" />
          </view>
        </view>

        <view class="border-t border-gray-100 py-20rpx">
          <view class="mb-20rpx flex items-center justify-between">
            <text class="text-28rpx text-[#333] font-medium">规格</text>
          </view>
          <view class="flex flex-wrap">
            <view
              v-for="(item, index) in skuOptions"
              :key="index"
              class="mb-15rpx mr-15rpx min-w-100rpx border rounded-full px-20rpx py-10rpx text-center text-26rpx"
              :class="[
                selectedSku === item
                  ? 'border-[#FF0000] bg-[#FFF0F0] text-[#FF0000]'
                  : 'border-gray-200 text-[#666] bg-white'
              ]"
              @click="selectSku(item)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>

        <view class="border-t border-gray-100 py-20rpx">
          <view class="my-20rpx flex items-center justify-between">
            <text class="text-28rpx text-[#333]">购买数量</text>
            <view class="flex items-center">
              <view
                class="h-48rpx w-48rpx flex items-center justify-center border border-gray-300 rounded-6rpx"
                @click="decreaseQuantity"
              >
                <text class="text-24rpx text-gray-500">-</text>
              </view>
              <view class="mx-2rpx h-48rpx w-80rpx flex items-center justify-center rounded-6rpx bg-gray-100">
                <text class="text-24rpx">{{ quantity }}</text>
              </view>
              <view
                class="h-48rpx w-48rpx flex items-center justify-center border border-gray-300 rounded-6rpx"
                @click="increaseQuantity"
              >
                <text class="text-24rpx text-gray-500">+</text>
              </view>
            </view>
          </view>
        </view>

        <view class="mx-20rpx mt-30rpx flex space-x-50rpx">
          <view
            class="h-80rpx flex flex-1 items-center justify-center rounded-40rpx from-[#fcb944] to-[#ffa929] bg-gradient-to-r text-30rpx text-white"
            @click="confirmAddCart"
          >
            加入购物车
          </view>
          <view
            class="h-80rpx flex flex-1 items-center justify-center rounded-40rpx from-[#ff3636] to-[#fe5856] bg-gradient-to-r text-30rpx text-white"
            @click="confirmBuyNow"
          >
            立即购买
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 底部操作栏 -->
    <view
      class="fixed bottom-0 left-0 right-0 z-99 h-140rpx flex items-center justify-center border-t border-gray-200 bg-white pl-30rpx pr-10rpx"
    >
      <view class="h-full w-100rpx flex-center" @click="navToHome">
        <image src="../../static/images/tabbar/home-select.png" class="h-50rpx w-50rpx" mode="aspectFit" />
      </view>
      <view class="mx-20rpx ml-120rpx h-80rpx w-180rpx flex flex-auto overflow-hidden rounded-17rpx">
        <view
          class="h-full flex flex-1 items-center justify-center text-30rpx text-white"
          style="background-image: linear-gradient(to right, #fcb944, #ffa929)"
          @click="handleAddCart"
        >
          加入购物车
        </view>
        <view
          class="h-full flex flex-1 items-center justify-center text-30rpx text-white"
          style="background-image: linear-gradient(to right, #ff3636, #fe5856)"
          @click="handleBuyNow"
        >
          立即购买
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getGoodsDetailApi } from '@/api'
import { useCart } from '@/hooks'

// 商品ID
const productId = ref<string | number>('0')

// 商品详情数据，默认为空对象
const productInfo = ref<any>({})

// 轮播图相关
const bannerList = computed(() => productInfo.value.main_image || [])
const currentBanner = ref<number>(0)

// 规格弹窗
const showSpecPopup = ref<boolean>(false)
const selectedSku = ref<any>(null)
const quantity = ref<number>(1)

// 规格选项（直接用接口skus）
const skuOptions = computed(() => productInfo.value.skus || [])

// 商品规格列表
const productSpecs = computed(() => [
  // {
  //   label: '服务',
  //   value: productInfo.value.related_services?.[0]?.name || '',
  //   showArrow: false,
  //   clickable: false
  // },
  {
    label: '品牌',
    value: productInfo.value.brand || '',
    showArrow: false,
    clickable: false
  },
  {
    label: '选择',
    value: selectedSku.value ? selectedSku.value.name : '请选择规格',
    showArrow: true,
    clickable: true
  }
])

// 处理轮播图变化
const handleSwiperChange = (e: any) => {
  currentBanner.value = e.detail.current
}

// 返回
const handleBack = () => {
  uni.navigateBack()
}

// 跳转首页
const navToHome = () => {
  uni.switchTab({ url: '/pages/tab/home/<USER>' })
}

// 打开规格弹窗
const openSpecPopup = () => {
  showSpecPopup.value = true
}

// 选择规格
const selectSku = (sku: any) => {
  selectedSku.value = sku
}

// 加入购物车
const handleAddCart = () => {
  if (!selectedSku.value) {
    openSpecPopup()
  } else {
    confirmAddCart()
  }
}

// 数量增减
const decreaseQuantity = () => {
  if (quantity.value > 1) quantity.value--
}
// 增加数量
const increaseQuantity = () => {
  if (quantity.value < selectedSku.value.stock) quantity.value++
}

// 确认加入购物车
const confirmAddCart = () => {
  if (!selectedSku.value) {
    uni.showToast({ title: '请选择完整规格', icon: 'none' })
    return
  }
  showSpecPopup.value = false
  // uni.showToast({ title: '已加入购物车', icon: 'success' })
  const { addToCart } = useCart()
  addToCart({
    product_id: productId.value,
    sku_id: selectedSku.value.id,
    quantity: quantity.value
  })
}

// 立即购买
const handleBuyNow = () => {
  if (!selectedSku.value) {
    openSpecPopup()
  } else {
    confirmBuyNow()
  }
}
const confirmBuyNow = () => {
  if (!selectedSku.value) {
    uni.showToast({ title: '请选择完整规格', icon: 'none' })
    return
  }
  showSpecPopup.value = false
  uni.navigateTo({ url: `/pages/shop/order-confirm?productId=${productId.value}` })
}

// 加载商品数据
const loadProductData = async (id: string | number) => {
  try {
    const [res] = await getGoodsDetailApi({ id })
    // 兼容接口嵌套结构
    const data = res?.data || {}
    productInfo.value = data
    // 默认选第一个sku
    if (data.skus && data.skus.length > 0) {
      selectedSku.value = data.skus[0]
      // 计算总库存
      productInfo.value.stock = data.skus.reduce(
        (previousValue: number, currentValue: any) => previousValue + currentValue.stock,
        0
      )
    }
  } catch (e) {
    uni.showToast({ title: '加载商品数据失败', icon: 'none' })
  }
}

onLoad((options: Record<string, string>) => {
  if (options.id) {
    productId.value = options.id
    loadProductData(options.id)
  }
})
</script>

<style lang="scss" scoped>
.flex-2 {
  flex: 2;
}

.rounded-left-40rpx {
  border-top-left-radius: 40rpx;
  border-bottom-left-radius: 40rpx;
}

.rounded-right-40rpx {
  border-top-right-radius: 40rpx;
  border-bottom-right-radius: 40rpx;
}

.spec-popup {
  max-height: 80vh;
}
</style>
