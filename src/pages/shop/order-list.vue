<route lang="json5" type="page">
{
  layout: 'theme', // 使用主题
  style: {
    navigationBarTitleText: '我的订单'
  }
}
</route>

<template>
  <view class="order-list">
    <wd-tabs v-model="tabIndex" swipeable animated sticky color="#FF3C29">
      <block v-for="(item, index) in tabList" :key="index">
        <wd-tab :title="item.name">
          <z-paging
            :ref="el => setPagingRef(el, index)"
            :fixed="false"
            :safe-area-inset-bottom="true"
            :refresher-enabled="true"
            :use-safe-area-placeholder="true"
            :show-scrollbar="false"
            height="calc(100vh - 185rpx - env(safe-area-inset-top) - env(safe-area-inset-bottom))"
            v-model="dataLists[index]"
            @query="(pageNo, pageSize) => queryList(pageNo, pageSize, index)"
            @refresherRefresh="() => onRefresh(index)"
          >
            <!-- 订单列表内容 -->
            <view class="p-20rpx">
              <view
                v-for="(order, idx) in dataLists[index]"
                :key="idx"
                class="order-item mb-20rpx overflow-hidden rounded-12rpx bg-white"
                @click="goToOrderDetail(order)"
              >
                <!-- 订单头部：日期和状态 -->
                <view class="order-header mx-30rpx flex justify-between p-(t-24rpx b-15rpx) border-b-1rpx-#EDEDED">
                  <text class="text-22rpx text-gray-600">{{ order.orderTime }}</text>
                  <text class="text-20rpx" :class="getStatusClass(order.status)">
                    {{ getStatusText(order.status) }}
                  </text>
                </view>

                <!-- 订单商品信息 -->
                <view class="order-content p-30rpx">
                  <view class="goods-info flex">
                    <!-- 商品图片 -->
                    <image
                      class="goods-image h-160rpx w-160rpx flex-shrink-0 rounded-8rpx"
                      :src="order.goodsImage"
                      mode="aspectFill"
                    />
                    <!-- 商品详情 -->
                    <view class="goods-detail ml-20rpx flex-1 flex flex-col">
                      <!-- 商品名称 -->
                      <view class="goods-name line-clamp-2 text-28rpx font-medium">{{ order.goodsName }}</view>

                      <!-- 商品规格和数量 -->
                      <view class="mt-16rpx flex justify-between items-center">
                        <view
                          class="goods-spec inline-block h-34rpx rounded-8rpx bg-#F2F2F2 px-10rpx text-center text-20rpx lh-34rpx"
                        >
                          {{ order.goodsSpecs }}
                        </view>
                        <text class="count text-24rpx text-gray-500">x{{ order.goodsCount }}</text>
                      </view>

                      <!-- 支付剩余时间和合计 -->
                      <view class="mt-auto flex justify-between items-center mb-20rpx">
                        <!-- 倒计时 - 只在待付款状态显示 -->
                        <view v-if="order.status === 'pending'">
                          <text class="text-16rpx">支付剩余时间 {{ order.countDown || '00:29:59' }}</text>
                        </view>
                        <view v-else class="flex-1"></view>

                        <!-- 总价 -->
                        <view class="flex items-center">
                          <text class="text-22rpx text-gray-600">共{{ order.goodsCount }}件</text>
                          <text class="text-28rpx text-primary font-bold ml-4rpx">&nbsp;¥{{ order.totalPrice }}</text>
                        </view>
                      </view>
                    </view>
                  </view>

                  <!-- 订单按钮 -->
                  <view class="order-footer flex justify-end gap-12rpx">
                    <template v-if="order.status === 'pending'">
                      <wd-button class="btn-primary-gray" size="small" @click.stop="cancelOrder(order)">
                        取消订单
                      </wd-button>
                      <wd-button class="btn-primary" size="small" @click.stop="payOrder(order)">去支付</wd-button>
                    </template>
                    <template v-else-if="order.status === 'processing'">
                      <wd-button class="btn-primary-gray" size="small" @click.stop="cancelOrder(order)">
                        取消订单
                      </wd-button>
                    </template>
                    <template v-else-if="order.status === 'shipped'">
                      <wd-button class="btn-primary-gray" size="small" @click.stop="viewLogistics(order)">
                        查看物流
                      </wd-button>
                      <wd-button class="btn-primary" size="small" @click.stop="confirmReceive(order)">
                        确认收货
                      </wd-button>
                    </template>
                    <template v-else-if="order.status === 'completed'">
                      <wd-button class="btn-primary-gray" size="small" @click.stop="deleteOrder(order)">
                        删除订单
                      </wd-button>
                      <wd-button class="btn-primary-gray" size="small" @click.stop="viewLogistics(order)">
                        查看物流
                      </wd-button>
                      <wd-button class="btn-primary" size="small" @click.stop="reorder(order)">再来一单</wd-button>
                    </template>
                    <template v-else-if="order.status === 'cancelled'">
                      <wd-button class="btn-primary-gray" size="small" @click.stop="deleteOrder(order)">
                        删除订单
                      </wd-button>
                      <wd-button class="btn-primary" size="small" @click.stop="reorder(order)">再来一单</wd-button>
                    </template>
                  </view>
                </view>
              </view>
            </view>

            <!-- 空状态提示 -->
            <template #empty>
              <view class="py-60rpx">
                <wd-status-tip image="content" :tip="getEmptyTip(index)" image-size="200rpx"></wd-status-tip>
              </view>
            </template>
          </z-paging>
        </wd-tab>
      </block>
    </wd-tabs>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

const tabIndex = ref(0)
const tabList = ref([
  { name: '全部' },
  { name: '待付款' },
  { name: '待发货' },
  { name: '待收货' },
  { name: '已完成' },
  { name: '已取消' }
])

// 为每个tab创建一个数据数组和z-paging实例引用
const pagingRefs = ref<any[]>([])
const dataLists = ref<any[][]>(
  Array(tabList.value.length)
    .fill(0)
    .map(() => [])
)

// 设置z-paging引用
const setPagingRef = (el: any, index: number) => {
  if (el) {
    pagingRefs.value[index] = el
  }
}

// 获取订单状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待付款',
    processing: '待发货',
    shipped: '待收货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取订单状态样式
const getStatusClass = (status: string) => {
  const statusClassMap: Record<string, string> = {
    pending: 'text-orange-500',
    processing: 'text-blue-500',
    shipped: 'text-green-500',
    completed: 'text-gray-600',
    cancelled: 'text-gray-400'
  }
  return statusClassMap[status] || ''
}

// 获取空状态提示文本
const getEmptyTip = (index: number) => {
  const tipMap = ['暂无订单', '暂无待付款订单', '暂无待发货订单', '暂无待收货订单', '暂无已完成订单', '暂无已取消订单']
  return tipMap[index] || '暂无订单'
}

// 查询订单列表
const queryList = async (pageNo: number, pageSize: number, tabIndex: number) => {
  try {
    // 模拟接口请求
    await new Promise(resolve => setTimeout(resolve, 500))

    // 获取不同状态的订单
    const statuses = ['pending', 'processing', 'shipped', 'completed', 'cancelled']
    let status = ''

    // 根据tabIndex确定要展示的订单状态
    if (tabIndex === 0) {
      // 全部订单，随机状态
      status = statuses[Math.floor(Math.random() * statuses.length)]
    } else if (tabIndex > 0 && tabIndex <= statuses.length) {
      // 特定状态订单
      status = statuses[tabIndex - 1]
    }

    // 模拟订单数据
    const mockOrders = []

    // 第三页时返回更少的数据，表示已到底
    const itemCount = pageNo >= 3 ? Math.floor(pageSize / 2) : pageSize

    for (let i = 0; i < itemCount; i++) {
      const orderId = (pageNo - 1) * pageSize + i + 1

      // 创建模拟订单数据
      const order = {
        id: orderId,
        orderNo: `ORDER${Date.now()}${orderId}`,
        orderTime: '2025-06-30 12:00',
        status: tabIndex === 0 ? statuses[Math.floor(Math.random() * statuses.length)] : status,
        goodsName: '商品名称商品名称商品名称商品名称',
        goodsImage: '/static/images/home/<USER>',
        goodsSpecs: '黑 12+256G',
        goodsPrice: '7709',
        goodsCount: 1,
        totalPrice: '7709',
        countDown: '00:29:59', // 添加倒计时属性
        goodsId: '10001' // 添加商品ID
      }

      mockOrders.push(order)
    }

    // 完成分页加载
    if (pagingRefs.value[tabIndex]) {
      pagingRefs.value[tabIndex].complete(mockOrders)
    }
  } catch (e) {
    console.error('加载订单列表失败', e)
    // 异常处理
    if (pagingRefs.value[tabIndex]) {
      pagingRefs.value[tabIndex].complete(false)
    }
  }
}

// 跳转到订单详情页
const goToOrderDetail = (order: any) => {
  uni.navigateTo({
    url: `/pages/shop/order-detail?id=${order.id}&orderNo=${order.orderNo}`
  })
}

// 取消订单
const cancelOrder = (order: any) => {
  uni.showModal({
    title: '提示',
    content: '确定要取消该订单吗？',
    success: res => {
      if (res.confirm) {
        // 实际项目中需调用取消订单API
        uni.showToast({
          title: '订单已取消',
          icon: 'success'
        })
        // 刷新当前列表
        onRefresh(tabIndex.value)
      }
    }
  })
}

// 支付订单
const payOrder = (order: any) => {
  uni.navigateTo({
    url: `/pages/shop/payment?orderNo=${order.orderNo}&amount=${order.totalPrice}`
  })
}

// 查看物流
const viewLogistics = (order: any) => {
  uni.showToast({
    title: '查看物流功能开发中',
    icon: 'none'
  })
}

// 确认收货
const confirmReceive = (order: any) => {
  uni.showModal({
    title: '提示',
    content: '确认已收到商品吗？',
    success: res => {
      if (res.confirm) {
        // 实际项目中需调用确认收货API
        uni.showToast({
          title: '确认收货成功',
          icon: 'success'
        })
        // 刷新当前列表
        onRefresh(tabIndex.value)
      }
    }
  })
}

// 删除订单
const deleteOrder = (order: any) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除该订单吗？',
    success: res => {
      if (res.confirm) {
        // 实际项目中需调用删除订单API
        uni.showToast({
          title: '订单已删除',
          icon: 'success'
        })
        // 刷新当前列表
        onRefresh(tabIndex.value)
      }
    }
  })
}

// 再来一单
const reorder = (order: any) => {
  // 直接跳转到确认订单页面
  uni.navigateTo({
    url: `/pages/shop/order-confirm?goodsId=${order.goodsId || ''}&goodsCount=${order.goodsCount}&isReorder=true&orderId=${order.id}`
  })
}

// 下拉刷新处理
const onRefresh = (index: number) => {
  // 重置页数，重新加载数据
  if (pagingRefs.value[index]) {
    pagingRefs.value[index].reload()
  }
}

// 监听tabIndex，当切换选项卡时刷新数据
watch(tabIndex, newVal => {
  // 切换选项卡时重新加载数据
  if (pagingRefs.value[newVal]) {
    // 如果当前tab没有数据，则刷新
    if (dataLists.value[newVal].length === 0) {
      pagingRefs.value[newVal].reload()
    }
  }
})

// 组件挂载完成后，加载当前选中tab的数据
onMounted(() => {
  // 组件挂载完成后，延迟一下再加载数据，确保pagingRefs已经初始化
  setTimeout(() => {
    if (pagingRefs.value[tabIndex.value]) {
      pagingRefs.value[tabIndex.value].reload()
    }
  }, 100)
})
</script>

<style lang="scss" scoped>
:deep(.wd-button--plain) {
  --button-plain-color: #999;
  --button-plain-border-color: #ddd;
}

:deep(.wd-button--small) {
  min-width: 128rpx;
  height: 60rpx;
  margin-right: 0;
  font-size: 24rpx;
}

:deep(.wd-tabs) {
  --wot-tabs-nav-line-bg-color: #ff3c29;
  background: transparent;
  .wd-tabs__nav {
    background: transparent;
  }
}

.order-item {
  // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.goods-detail {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 160rpx;
}

.btn-primary,
.btn-primary-gray {
  @apply btn-primary w-auto min-w-138rpx h-52rpx lh-52rpx !rounded-10rpx !text-22rpx;
}

.btn-primary-gray {
  @apply text-22rpx !bg-[#F0F0F0] !text-[#000];
  font-size: 22rpx !important;
  background-image: unset !important;
}
</style>
