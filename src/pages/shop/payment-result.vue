<route lang="json5">
{
  style: {
    navigationBarTitleText: '支付结果',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="payment-result">
    <view class="m-(x-32rpx t-100rpx) flex-col-center rounded-16rpx bg-white p-(x-40rpx b-60rpx)">
      <!-- 图标区域 -->
      <view class="mt--51rpx">
        <view v-if="isSuccess" class="h-102rpx w-100rpx flex-center rounded-full bg-primary">
          <wd-icon name="check" size="60rpx" color="#fff"></wd-icon>
        </view>
        <view v-else class="h-102rpx w-100rpx flex-center rounded-full bg-primary">
          <wd-icon name="close" size="60rpx" color="#fff"></wd-icon>
        </view>
      </view>

      <!-- 文字区域 -->
      <view class="mt-50rpx text-center">
        <view class="text-36rpx text-[#333333] font-medium">{{ isSuccess ? '支付成功' : '支付失败' }}</view>
        <view v-if="isSuccess" class="mt-10rpx text-24rpx text-[#333]">
          支付金额：
          <text class="text-primary">¥{{ amount }}</text>
        </view>
      </view>

      <!-- 按钮区域 -->
      <view class="w-full px-40rpx" :class="isSuccess ? 'mt-10rpx' : 'mt-70rpx'">
        <view v-if="!isSuccess" class="btn-primary rounded-42rpx text-32rpx !h-84rpx" @click="rePay">重新支付</view>

        <view
          class="mt-30rpx h-90rpx flex-center rounded-45rpx text-32rpx font-medium"
          :class="isSuccess ? 'bg-[#FF3C29] text-white' : 'bg-white text-[#777777] border border-[#777777]'"
          @click="goHome"
        >
          返回首页
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineOptions({
  name: 'PayResult'
})

// 状态
const isSuccess = ref(false)
const amount = ref('0.00')
const orderId = ref('')

// 页面加载
onLoad(options => {
  // 解析参数
  if (options.status === 'success') {
    isSuccess.value = true
  } else {
    isSuccess.value = false
  }

  if (options.amount) {
    amount.value = options.amount
  }

  if (options.orderId) {
    orderId.value = options.orderId
  }
})

// 重新支付
function rePay() {
  if (orderId.value) {
    uni.redirectTo({
      url: `/pages/shop/payment?orderId=${orderId.value}`
    })
  } else {
    uni.navigateBack()
  }
}

// 返回首页
function goHome() {
  uni.switchTab({
    url: '/pages/tab/home/<USER>'
  })
}
</script>
