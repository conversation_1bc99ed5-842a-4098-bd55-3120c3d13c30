<route lang="json5" type="page">
{
  layout: 'theme', // 使用主题
  style: {
    navigationBarTitleText: '全部分类'
  }
}
</route>

<template>
  <view class="wraper">
    <wd-sidebar v-model="activeIndex" @change="handleChange">
      <wd-sidebar-item
        v-for="(item, index) in categories"
        :key="item.id"
        :value="index"
        :label="item.name"
        :disabled="item.disabled"
      />
    </wd-sidebar>
    <view class="content">
      <transition name="fade-slide">
        <scroll-view
          v-if="categories.length > 0 && activeIndex < categories.length"
          class="category"
          scroll-y
          scroll-with-animation
          :show-scrollbar="false"
          :scroll-top="scrollTop"
          :throttle="false"
        >
          <template v-if="isLoading">
            <view class="loading-container">
              <wd-loading color="red" />
            </view>
          </template>
          <template v-else>
            <view class="category-title">{{ categories[activeIndex].name }}</view>

            <view
              v-if="!categories[activeIndex].child || categories[activeIndex].child.length === 0"
              class="empty-container"
            >
              <wd-status-tip image="content" tip="暂无子分类内容" image-size="160" />
            </view>
            <view v-else class="grid-container">
              <view
                v-for="subItem in getSubCategories(categories[activeIndex])"
                :key="subItem.id"
                class="grid-item animate-item"
                @click="navToSubcategory(subItem)"
              >
                <wd-img
                  :src="subItem?.icon"
                  class="grid-img"
                  mode="aspectFit"
                  :loading-class="'image-loading'"
                  :error-class="'image-error'"
                  :show-transition="true"
                />
                <text class="grid-text">{{ subItem.name }}</text>
              </view>
            </view>
          </template>
        </scroll-view>
      </transition>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getGoodsCategoryListApi } from '@/api'

interface SubCategory {
  id: number
  name: string
  icon: string | null
  parent_id: number
  level: number
  child?: SubCategory[]
}

interface Category {
  id: number
  name: string
  icon?: string | null
  parent_id: number
  level: number
  disabled?: boolean
  child: SubCategory[] | any[] // 使用any[]兼容可能的多级结构
}

const activeIndex = ref<number>(0) // 初始值设为0，将由onLoad中的id参数决定
const isLoading = ref<boolean>(true)
const scrollTop = ref<number>(0)
const categories = ref<Category[]>([])

// 从页面参数中获取id值作为默认activeIndex
onLoad(options => {
  isLoading.value = true

  // 获取商品菜单一级分类列表
  getGoodsCategoryListApi({ type: 'all' })
    .unwrap()
    .then(({ data }) => {
      if (data && Array.isArray(data)) {
        categories.value = data

        // 根据id参数查找匹配的分类索引
        if (options.id) {
          const categoryId = parseInt(options.id as string)
          const index = categories.value.findIndex(item => item.id === categoryId)
          if (index !== -1) {
            activeIndex.value = index
          }
        }

        // 初始化完成后结束加载状态
        isLoading.value = false
      }
    })
    .catch(() => {
      isLoading.value = false
      uni.showToast({
        title: '加载分类数据失败',
        icon: 'none'
      })
    })
})

// 获取子分类列表，处理多级结构
const getSubCategories = (category: Category): SubCategory[] => {
  if (!category || !category.child || category.child.length === 0) return []

  // 如果子分类中第一个有child属性，说明是二级分类
  if (category.child[0]?.child) {
    // 返回所有二级分类下的子类合并数组
    const result: SubCategory[] = []
    category.child.forEach((item: any) => {
      if (item.child && Array.isArray(item.child)) {
        result.push(...item.child)
      }
    })
    return result
  }

  // 直接返回子分类
  return Array.isArray(category.child) ? (category.child as SubCategory[]) : []
}

// 处理分类切换
const handleChange = ({ value }: { value: number }) => {
  // 如果索引无效，不处理
  if (value < 0 || value >= categories.value.length) return

  isLoading.value = true
  activeIndex.value = value

  // 重置滚动位置
  scrollTop.value = -1
  nextTick(() => {
    scrollTop.value = 0
    isLoading.value = false
  })
}

// 跳转到子分类页面
const navToSubcategory = (item: SubCategory) => {
  uni.navigateTo({
    url: `/pages/shop/shop-list?categoryId=${item.id}&categoryLevel=${item.level}`
  })
}
</script>

<style lang="scss" scoped>
:deep(.wot-theme-light) {
  & > .min-h-100vh {
    min-height: auto !important;
  }
}
.wraper {
  display: flex;
  height: calc(100vh - var(--window-top));
  /* stylelint-disable-next-line function-no-unknown */
  height: calc(100vh - var(--window-top) - constant(safe-area-inset-bottom));
  height: calc(100vh - var(--window-top) - env(safe-area-inset-bottom));
  overflow: hidden;
}

// 分类切换动画 - 简化效果
.fade-slide-enter-active {
  transition: all 0.25s ease;
}
.fade-slide-leave-active {
  transition: all 0.15s ease;
}
.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(10px);
}
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

// 子分类项目入场动画 - 简化效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-item {
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

.wd-sidebar {
  width: 236rpx;

  :deep(.wd-sidebar-item--prefix) {
    border-bottom-right-radius: var(--wot-sidebar-border-radius, 20rpx);
  }

  :deep(.wd-sidebar__padding) {
    background: var(--theme-bg-color);
  }

  :deep(.wd-sidebar-item) {
    height: 136rpx;
    font-size: 30rpx;
    background: var(--theme-bg-color);
    transition: all 0.3s ease;
  }

  :deep(.wd-sidebar-item--active) {
    color: var(--primary-color);
    background: #fff;

    &::before {
      left: -3rpx;
      background: var(--primary-color) !important;
      transition: all 0.3s ease;
    }
  }
}

.content {
  flex: 1;
  margin: 30rpx 30rpx 30rpx 0;
  overflow: hidden;
  background: #fff;
  border-radius: 22rpx;
  transition: transform 0.3s ease;

  :deep(.uni-scroll-view-content) {
    border-radius: 22rpx;
  }
}

.category {
  box-sizing: border-box;
  height: 100%;
}

.category-title {
  padding: 20rpx 30rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 20vh 0;
}

.empty-container {
  display: flex;
  justify-content: center;
  padding: 60rpx 0;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 0 30rpx 30rpx;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.grid-img {
  width: 120rpx;
  height: 120rpx;
  // border: 1px solid #f5f5f5;
  border-radius: 8rpx;
}

.grid-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.image-loading,
.image-error {
  background-color: #f5f5f5;
  border-radius: 8rpx;
}
</style>
