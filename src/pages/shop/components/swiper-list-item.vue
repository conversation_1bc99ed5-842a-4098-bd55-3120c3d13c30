<template>
  <view class="swiper-list-container">
    <z-paging ref="paging" v-model="orderList" @query="queryOrderList">
      <!-- 订单列表内容 -->
      <view class="order-list-content pb-safe">
        <view v-for="(order, idx) in orderList" :key="idx" class="order-item mb-20rpx bg-white">
          <!-- 订单头部：日期和状态 -->
          <view class="order-header flex justify-between border-b border-gray-100 p-30rpx">
            <text class="text-26rpx text-gray-600">{{ order.orderTime }}</text>
            <text class="text-26rpx" :class="getStatusClass(order.status)">
              {{ getStatusText(order.status) }}
            </text>
          </view>

          <!-- 订单商品信息 -->
          <view class="order-content p-30rpx">
            <view class="goods-info flex">
              <image
                class="goods-image h-180rpx w-180rpx flex-shrink-0 rounded-8rpx"
                :src="order.goodsImage"
                mode="aspectFill"
              />
              <view class="goods-detail ml-20rpx flex-1">
                <view class="goods-name line-clamp-2 text-28rpx text-gray-800">{{ order.goodsName }}</view>
                <view class="goods-spec mt-10rpx text-24rpx text-gray-500">{{ order.goodsSpecs }}</view>
                <view class="goods-price-count mt-20rpx flex justify-between">
                  <text class="price text-28rpx">¥{{ order.goodsPrice }}</text>
                  <text class="count text-24rpx text-gray-500">x{{ order.goodsCount }}</text>
                </view>
              </view>
            </view>

            <!-- 订单总价 -->
            <view class="order-total mt-20rpx flex justify-end">
              <text class="text-26rpx">共{{ order.goodsCount }}件 合计:</text>
              <text class="text-28rpx text-primary font-bold">¥{{ order.totalPrice }}</text>
            </view>

            <!-- 订单底部按钮 -->
            <view class="order-footer mt-20rpx flex justify-end">
              <template v-if="order.status === 'pending'">
                <wd-button plain size="small" class="mr-20rpx">查看订单</wd-button>
                <wd-button type="primary" size="small">去支付</wd-button>
              </template>
              <template v-else-if="order.status === 'processing'">
                <wd-button plain size="small" class="mr-20rpx">查看物流</wd-button>
                <wd-button type="primary" size="small">提醒发货</wd-button>
              </template>
              <template v-else-if="order.status === 'shipped'">
                <wd-button plain size="small" class="mr-20rpx">查看物流</wd-button>
                <wd-button type="primary" size="small">确认收货</wd-button>
              </template>
              <template v-else-if="order.status === 'completed'">
                <wd-button plain size="small" class="mr-20rpx">删除订单</wd-button>
                <wd-button plain size="small" class="mr-20rpx">查看物流</wd-button>
                <wd-button type="primary" size="small">再来一单</wd-button>
              </template>
              <template v-else-if="order.status === 'cancelled'">
                <wd-button plain size="small" class="mr-20rpx">删除订单</wd-button>
                <wd-button type="primary" size="small">再来一单</wd-button>
              </template>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态提示 -->
      <template #empty>
        <view class="py-60rpx">
          <wd-status-tip image="content" :tip="getEmptyTip()" image-size="200rpx"></wd-status-tip>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script lang="ts">
// 在Vue 3中，defineProps和defineExpose已经全局可用，不需要手动导入
// 但是需要使用<script lang="ts">而不是<script setup lang="ts">
import { ref, watch } from 'vue'

export default {
  props: {
    tabIndex: {
      type: Number,
      default: 0
    },
    currentIndex: {
      type: Number,
      default: 0
    }
  },
  setup(props) {
    // z-paging组件引用
    const paging = ref<any>(null)

    // 订单列表数据
    const orderList = ref<any[]>([])

    // 获取订单状态文本
    const getStatusText = (status: string) => {
      const statusMap: Record<string, string> = {
        pending: '待付款',
        processing: '待发货',
        shipped: '待收货',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || '未知状态'
    }

    // 获取订单状态样式
    const getStatusClass = (status: string) => {
      const statusClassMap: Record<string, string> = {
        pending: 'text-orange-500',
        processing: 'text-blue-500',
        shipped: 'text-green-500',
        completed: 'text-gray-600',
        cancelled: 'text-gray-400'
      }
      return statusClassMap[status] || ''
    }

    // 获取空状态提示文本
    const getEmptyTip = () => {
      const tipMap = [
        '暂无订单',
        '暂无待付款订单',
        '暂无待发货订单',
        '暂无待收货订单',
        '暂无已完成订单',
        '暂无已取消订单'
      ]
      return tipMap[props.tabIndex] || '暂无订单'
    }

    // 查询订单列表
    const queryOrderList = async (pageNo: number, pageSize: number) => {
      try {
        // 模拟接口请求
        await new Promise(resolve => setTimeout(resolve, 500))

        // 获取不同状态的订单
        const statuses = ['pending', 'processing', 'shipped', 'completed', 'cancelled']
        let status = ''

        // 根据tabIndex确定要展示的订单状态
        if (props.tabIndex === 0) {
          // 全部订单，随机状态
          status = statuses[Math.floor(Math.random() * statuses.length)]
        } else if (props.tabIndex > 0 && props.tabIndex <= statuses.length) {
          // 特定状态订单
          status = statuses[props.tabIndex - 1]
        }

        // 模拟订单数据
        const mockOrders = []

        // 第三页时返回更少的数据，表示已到底
        const itemCount = pageNo >= 3 ? Math.floor(pageSize / 2) : pageSize

        for (let i = 0; i < itemCount; i++) {
          const orderId = (pageNo - 1) * pageSize + i + 1

          // 创建模拟订单数据
          const order = {
            id: orderId,
            orderNo: `ORDER${Date.now()}${orderId}`,
            orderTime: '2025-06-30 12:00',
            status: props.tabIndex === 0 ? statuses[Math.floor(Math.random() * statuses.length)] : status,
            goodsName: '商品名称商品名称商品名称商品名称',
            goodsImage: '/static/images/home/<USER>',
            goodsSpecs: '黑 12+256G',
            goodsPrice: '7709',
            goodsCount: 1,
            totalPrice: '7709'
          }

          mockOrders.push(order)
        }

        // 完成分页加载
        paging.value?.complete(mockOrders)
      } catch (e) {
        console.error('加载订单列表失败', e)
        // 异常处理
        paging.value?.complete(false)
      }
    }

    // 重新加载列表数据
    const reload = () => {
      paging.value?.reload()
    }

    // 监听tabIndex和currentIndex，当当前tab激活时刷新数据
    watch(
      () => props.currentIndex,
      newVal => {
        if (newVal === props.tabIndex) {
          // 当前tab被激活，刷新数据
          paging.value?.reload()
        }
      }
    )

    // 暴露组件方法
    return {
      paging,
      orderList,
      getStatusText,
      getStatusClass,
      getEmptyTip,
      queryOrderList,
      reload
    }
  }
}
</script>

<style lang="scss" scoped>
.swiper-list-container {
  height: 100%;
}

.order-list-content {
  padding: 20rpx;
}

.order-item {
  margin-bottom: 20rpx;
  overflow: hidden;
  border-radius: 12rpx;
}

:deep(.wd-button--plain) {
  --button-plain-color: #999;
  --button-plain-border-color: #ddd;
}

:deep(.wd-button--small) {
  min-width: 140rpx;
  height: 60rpx;
  font-size: 24rpx;
}
</style>
