<route lang="json5" type="page">
{
  layout: 'theme', // 使用主题
  style: {
    navigationStyle: 'custom'
  }
}
</route>

<template>
  <view class="box-border min-h-screen flex flex-col bg-[#F3F5F7]">
    <!-- 顶部导航栏 -->
    <view
      class="relative h-400rpx w-full bg-[length:100%_auto] bg-[url('/static/images/shop/active-top-bg.png')] bg-top bg-no-repeat"
    >
      <!-- 返回按钮 -->
      <view class="pos-absolute left-30rpx top-60rpx" @click="handleBack">
        <wd-icon name="thin-arrow-left" size="30rpx" color="#fff"></wd-icon>
      </view>

      <!-- 活动标题 -->
      <view class="h-full w-full flex flex-col items-center justify-center">
        <wd-img
          class="mt-40rpx"
          height="138rpx"
          mode="heightFix"
          :src="`/static/images/shop/active-${activityType}.png`"
        ></wd-img>
        <view class="top-sub-tip mt-40rpx text-34rpx text-[#EEAAB5] tracking-15rpx">让选择更简单</view>
      </view>
    </view>

    <!-- 活动商品列表 -->
    <view class="mt-20rpx px-20rpx">
      <view class="mb-20rpx rounded-12rpx bg-white p-20rpx">
        <shop-title :title="`${activityType === 1 ? '人气爆款' : '黄金专场'}精选`" />
      </view>

      <!-- 使用z-paging组件 -->
      <z-paging ref="paging" v-model="goodsList" @query="queryGoodsList" :fixed="false" :use-page-scroll="true">
        <view class="grid grid-cols-2 gap-20rpx">
          <goods-item v-for="item in goodsList" :key="item.id" :goods="item" @click="handleGoodsClick(item)" />
        </view>

        <!-- 空数据状态 -->
        <template #empty>
          <view class="py-60rpx flex flex-col items-center">
            <wd-icon name="warning-filling" size="80rpx" color="#ccc"></wd-icon>
            <text class="mt-20rpx text-28rpx text-gray-400">暂无商品数据</text>
          </view>
        </template>
      </z-paging>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getGoodsListApi } from '@/api'

// 定义商品类型
interface GoodsItem {
  id: string | number
  main_image: string
  title?: string
  name: string
  price?: string | number
  min_price: number
  sales_volume: number
  sold?: string | number
}

// 活动类型 1: 人气爆款, 2: 黄金专场
const activityType = ref<number>(1)
// z-paging组件引用
const paging = ref<any>(null)
// 商品列表数据
const goodsList = ref<GoodsItem[]>([])

onLoad((options?: { type?: string | number }) => {
  activityType.value = Number(options?.type) || 1

  // 根据活动类型动态设置页面标题
  const title = activityType.value === 1 ? '人气爆款' : '黄金专场'
  uni.setNavigationBarTitle({
    title: title
  })
})

// 查询商品列表数据
const queryGoodsList = async (pageNo: number, pageSize: number) => {
  try {
    // 根据活动类型设置不同的请求参数
    const params: API.Goods.ListParams = {
      page: pageNo,
      size: pageSize,
      is_hot: activityType.value === 1 ? 1 : 2,
      // sort: activityType.value === 2 ? 'price' : 'created_at',
      order: 'DESC'
    }

    // 调用API获取商品列表
    const [res] = await getGoodsListApi(params)

    // 将返回的数据交给z-paging处理
    if (res && res.data) {
      paging.value.complete(res.data)
    } else {
      paging.value.complete(false)
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    // 请求失败时通知z-paging
    paging.value.complete(false)
  }
}

// 处理商品点击
const handleGoodsClick = (goods: any) => {
  uni.navigateTo({
    url: `/pages/shop/shop-detail?id=${goods.id}`
  })
}

// 处理返回按钮点击
const handleBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.top-sub-tip {
  display: flex;
  align-items: center;

  &::before,
  &::after {
    display: block;
    width: 100rpx;
    height: 34rpx;
    content: '';
    background: url('/static/images/shop/active-title-icon.png') no-repeat center center/contain;
  }

  &::before {
    margin-right: 30rpx;
    transform: rotate(180deg);
  }

  &::after {
    margin-left: 30rpx;
  }
}
</style>
