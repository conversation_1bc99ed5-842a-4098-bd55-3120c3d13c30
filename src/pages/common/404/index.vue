<route lang="json5" type="page">
{
  layout: 'theme', // 使用主题
  style: {
    navigationStyle: 'custom'
  }
}
</route>

<template>
  <view class="min-h-screen flex flex-col bg-white">
    <wd-navbar left-arrow @click-left="handleBack" />
    <wd-status-tip image="/static/images/404.png" tip="页面不存在" image-size="380" />
  </view>
</template>

<script setup lang="ts">
function handleBack() {
  uni.navigateBack()
}
</script>
