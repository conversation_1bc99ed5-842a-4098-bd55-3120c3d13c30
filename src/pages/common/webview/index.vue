<route lang="json5" type="page">
{
  layout: 'theme', // 使用主题
  style: {
    // navigationStyle: 'custom'
  }
}
</route>

<template>
  <web-view class="h-full" :src="url" />
</template>

<script setup lang="ts">
const url = ref<string>('')

onLoad((options: any) => {
  // 获取并设置URL
  if (options.url) url.value = decodeURIComponent(options.url)

  // 动态设置页面标题
  if (options.title) {
    uni.setNavigationBarTitle({
      title: decodeURIComponent(options.title)
    })
  }
})
</script>
