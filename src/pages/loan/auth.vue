<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '身份认证',
    navigationStyle: 'default',
    'app-plus': {
      scrollIndicator: 'none'
    }
  }
}
</route>

<template>
  <view class="page-container flex flex-col bg-gray-100 min-h-screen pb-120rpx">
    <!-- 服务授权弹窗 -->
    <wd-popup
      class="rounded-12rpx overflow-hidden"
      v-model="showAuthDialog"
      :close-on-click-modal="false"
      :z-index="999"
      lock-scroll
    >
      <view class="auth-dialog-container">
        <!-- Logo -->
        <wd-img></wd-img>

        <!-- 标题 -->
        <view class="auth-title">服务授权</view>

        <!-- 描述 -->
        <view class="auth-desc">您即将登录并开始使用花花卡服务</view>

        <!-- 协议 - 单选框 -->
        <view class="auth-radio-container">
          <wd-checkbox v-model="agreementChecked" checked-value="agree" custom-color="#FE3635">
            <text class="auth-radio-text">如您决定使用本服务，请勾选同意</text>
            <text class="auth-link" @click.stop="openPolicy('agreement')">《授权协议》</text>
            <text class="auth-link" @click.stop="openPolicy('privacy')">《注册协议》</text>
          </wd-checkbox>
        </view>

        <!-- 同意按钮 -->
        <wd-button
          type="primary"
          @click="handleAuthConfirm"
          :disabled="agreementChecked !== 'agree'"
          block
          class="auth-button"
        >
          同意
        </wd-button>
      </view>
    </wd-popup>

    <view class="p-30rpx">
      <view class="text-34rpx">
        请确保本人认证 预估最高可得
        <text class="text-#FF8D1A">50000元</text>
      </view>
      <view class="m-(t-10rpx b-70rpx) text-(#666 24rpx)">用于身份验证及借款评估，全程保证隐私安全</view>
      <view class="text-(#666 24rpx) mb-28rpx">请上传您的身份证原件，确保照片清晰、四角完整</view>

      <view class="flex justify-between mt-30rpx">
        <view class="w-50% h-220rpx rounded-lg" @click="onUpload('Front')">
          <image
            v-if="!formData.idCardFrontPic"
            class="w-full h-full"
            src="@/static/images/loan/front.png"
            mode="aspectFit"
          ></image>
          <image v-else :src="formData.idCardFrontPic" class="w-95% h-220rpx rounded-lg" mode="aspectFill"></image>
        </view>
        <view class="w-50% h-220rpx rounded-lg" @click="onUpload('Back')">
          <image
            v-if="!formData.idCardBackPic"
            class="w-full h-full"
            src="@/static/images/loan/contrary.png"
            mode="aspectFit"
          ></image>
          <image v-else :src="formData.idCardBackPic" class="w-95% h-220rpx rounded-lg" mode="aspectFill"></image>
        </view>
      </view>

      <!-- 只有在OCR识别完成后才显示这个提示 -->
      <view
        class="text-(26rpx #666) mt-4"
        v-if="formData.idCardFrontPic && formData.idCardBackPic && formData.realName"
      >
        请确认以下信息是否正确，可手动修改
      </view>

      <!-- 身份证识别信息 - 只有在两张图片上传并识别完成后才显示 -->
      <wd-cell-group
        v-if="formData.idCardFrontPic && formData.idCardBackPic && formData.realName"
        border
        custom-class="real-name bg-white rounded-lg overflow-hidden mt-4"
      >
        <wd-cell title="真实姓名" title-width="150rpx">
          <wd-input v-model="formData.realName" placeholder="请输入内容" no-border />
        </wd-cell>
        <wd-cell title="身份证号" title-width="150rpx">
          <wd-input v-model="formData.idCardNumber" placeholder="请输入内容" :maxlength="18" no-border />
        </wd-cell>
        <wd-cell title="有效期" title-width="150rpx">
          <wd-datetime-picker
            v-model="formData.idCardExpirationDate"
            placeholder="请选择日期"
            value-format="yyyy-MM-dd"
            :min-date="minDateTimestamp"
            :max-date="maxDateTimestamp"
            :display-format="formatDate"
            use-label-slot
          >
            <wd-input
              slot="trigger"
              v-model="formData.idCardExpirationDate"
              placeholder="请选择日期"
              no-border
              readonly
            />
          </wd-datetime-picker>
        </wd-cell>
      </wd-cell-group>

      <!-- 加载中状态 - 两张照片上传后但OCR识别中 -->
      <view
        v-if="formData.idCardFrontPic && formData.idCardBackPic && !formData.realName"
        class="bg-white rounded-lg mt-4 p-4 mb-120rpx flex flex-col items-center justify-center"
      >
        <view class="text-sm text-gray-800 mb-4">正在识别身份证信息...</view>
        <wd-loading color="#FE3635" />
      </view>

      <!-- 温馨提示 - 只有在OCR识别完成后才显示 -->
      <view
        v-else-if="formData.idCardFrontPic && formData.idCardBackPic && formData.realName"
        class="bg-white rounded-lg mt-4 p-4 mb-120rpx"
      >
        <view class="font-normal text-sm text-gray-800 mb-4">温馨提示:</view>
        <view class="text-xs text-gray-500 mb-3">
          上传身份证正反面进行实名认证是为了对您的基本信息，确保本人使用，防止财产诈骗。
        </view>
        <view class="text-xs text-gray-500 mb-3">请上传本人身份证照片，非本人信息无法通过审核。</view>
        <view class="text-xs text-gray-500 mb-3">确保证件边框完整，文字清晰可见。</view>
        <view class="text-xs text-gray-500">可现场拍摄或从手机相册获取。</view>
      </view>
    </view>

    <!-- 修改footer部分 -->
    <!-- footer -->
    <view class="footer-fixed">
      <view class="p-30rpx">
        <view class="mb-3">
          <wd-checkbox v-model="checked" checked-color="#FE3635" custom-color="#FE3635">
            <text class="text-xs text-gray-500">我已阅读并同意</text>
            <text class="text-xs text-primary" @click.stop="openAgreement">《身份认证服务协议》</text>
          </wd-checkbox>
        </view>

        <wd-button block @click="onSubmit" custom-class="btn-primary !h-72rpx" :disabled="!isFormValid">
          下一步
        </wd-button>

        <view class="text-(22rpx #7A7A7A center) mt-20rpx pb-[var(--safe-area-inset-bottom)]">
          平台承诺保证您的信息安全
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

defineOptions({
  name: 'PageAuthVerification'
})

// 服务授权弹窗状态
const showAuthDialog = ref(false)
const agreementChecked = ref('')

// 监听弹窗状态变化控制页面滚动
watch(showAuthDialog, newVal => {
  if (newVal) {
    // 禁用页面滚动
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.width = '100%'
  } else {
    // 启用页面滚动
    document.body.style.overflow = ''
    document.body.style.position = ''
    document.body.style.width = ''
  }
})

// 处理授权确认
const handleAuthConfirm = () => {
  if (agreementChecked.value === 'agree') {
    showAuthDialog.value = false
  } else {
    uni.showToast({
      title: '请阅读并同意协议',
      icon: 'none'
    })
  }
}

// 表单数据
const formData = ref({
  idCardFrontPic: '',
  idCardBackPic: '',
  realName: '',
  idCardNumber: '',
  idCardExpirationDate: ''
})

// 日期限制 - 转换为时间戳
const startDate = ref('2010-01-01')
const endDate = ref('2050-12-31')
const minDateTimestamp = ref(new Date('2010-01-01').getTime())
const maxDateTimestamp = ref(new Date('2050-12-31').getTime())

// 日期格式化函数
const formatDate = date => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 协议勾选
const checked = ref(false)

// 日期选择事件
const bindDateChange = (e: any) => {
  formData.value.idCardExpirationDate = e.detail.value
}

// 打开协议
const openAgreement = () => {
  uni.navigateTo({
    url: '/pages/common/webview/index?title=身份认证服务协议&url=https://example.com/agreement'
  })
}

// 打开用户协议或隐私政策
const openPolicy = (type: 'privacy' | 'agreement') => {
  const title = type === 'privacy' ? '隐私政策' : '用户协议'
  uni.navigateTo({
    url: `/pages/common/webview/index?title=${title}&url=https://example.com/${type}`
  })
}

// 上传身份证图片
const onUpload = (type: string) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      const tempFilePaths = res.tempFilePaths[0]

      if (type === 'Front') {
        formData.value.idCardFrontPic = tempFilePaths
        // 模拟OCR识别成功后获取信息
        if (formData.value.idCardBackPic) {
          simulateOcrRecognition()
        }
      } else {
        formData.value.idCardBackPic = tempFilePaths
        // 模拟OCR识别成功后获取信息
        if (formData.value.idCardFrontPic) {
          simulateOcrRecognition()
        }
      }
    }
  })
}

// 模拟OCR识别
const simulateOcrRecognition = () => {
  uni.showLoading({ title: '正在识别' })

  setTimeout(() => {
    // 模拟识别结果
    uni.hideLoading()
    formData.value.realName = '陈成领'
    formData.value.idCardNumber = '220523196204064015'
    formData.value.idCardExpirationDate = '2030-01-01'
  }, 1500)
}

// 判断表单是否有效（用于禁用/启用下一步按钮）
const isFormValid = computed(() => {
  return (
    checked.value &&
    formData.value.idCardFrontPic &&
    formData.value.idCardBackPic &&
    formData.value.realName &&
    formData.value.idCardNumber &&
    formData.value.idCardExpirationDate
  )
})

// 提交实名认证
const onSubmit = () => {
  if (!checkInfo()) return

  uni.showLoading({ title: '提交中' })

  // 模拟API请求
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '认证信息提交成功',
      icon: 'success'
    })
    let url =
      'https://kyc.qcloud.com/s/web/h5?appId=IDAZC8GK&version=1.0.0&orderNo=3f2651fc1ce24183a05dc4e682d16fac&faceId=tx00a732e189f104a2131011903b4f8e&userId=3f2651fc1ce24183a05dc4e682d16fac&version=1.0.0&sign=EA4121B121299AA8AEE4C1EB31AEDC4D50095F18'

    // 跳转到下一步
    setTimeout(() => {
      // #ifdef H5
      // window.open(url, '_blank')
      // #endif

      // #ifdef APP
      uni.navigateTo({ url: '/pages/loan/info' })
      // #endif
      // uni.navigateTo({ url: '/pages/common/webview/index?title=人脸识别&url=' + url })
    }, 1000)
  }, 1000)
}

// 验证信息
const checkInfo = (): boolean => {
  if (!(formData.value.idCardFrontPic && formData.value.idCardBackPic)) {
    uni.showToast({ icon: 'none', title: '请上传身份证照片' })
    return false
  }

  if (!formData.value.realName || formData.value.realName.length < 2) {
    uni.showToast({ icon: 'none', title: '请输入正确的姓名' })
    return false
  }

  if (
    !formData.value.idCardNumber ||
    !/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(formData.value.idCardNumber)
  ) {
    uni.showToast({ icon: 'none', title: '请输入正确的身份证号码' })
    return false
  }

  if (!formData.value.idCardExpirationDate) {
    uni.showToast({ icon: 'none', title: '请选择身份证有效期' })
    return false
  }

  if (!checked.value) {
    uni.showToast({ icon: 'none', title: '请阅读并同意协议' })
    return false
  }

  return true
}
</script>

<style lang="scss" scoped>
.card-item {
  position: relative;
  display: flex;

  .title {
    width: 160rpx;
    line-height: 104rpx;
  }

  :deep(.uni-easyinput__content) {
    height: 100%;
  }
}

.uni-input {
  display: flex;
  align-items: center;
  height: 104rpx;
  font-size: 14px;
}

/* 服务授权弹窗样式 */
.auth-dialog-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 640rpx;
  padding: 48rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.auth-logo {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  background-color: #fe3635;
  border-radius: 16rpx;
}

.auth-title {
  margin-bottom: 24rpx;
  font-size: 36rpx;
  font-weight: 500;
  color: #000;
}

.auth-desc {
  margin-bottom: 48rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.auth-radio-container {
  width: 100%;
  margin-bottom: 48rpx;
}

.auth-radio-text {
  font-size: 28rpx;
  color: #666;
}

.auth-link {
  font-size: 28rpx;
  color: #fe3635;
}

.auth-button {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  background-color: #fe3635 !important;
  border-color: #fe3635 !important;
  border-radius: 9999rpx !important;
}

:deep(.wd-checkbox__label) {
  font-size: 28rpx !important;
}

:deep(.wd-checkbox__shape) {
  transform: scale(0.8);
}

:deep(.wd-checkbox__checked) {
  background-color: #fe3635 !important;
  border-color: #fe3635 !important;
}

:deep(.wd-popup) {
  // height: 470rpx;
  border-radius: 12rpx;
}

.card-input {
  flex: 1;
}

:deep(.wd-input__inner) {
  height: 104rpx;
  font-size: 28rpx;
}

:deep(.wd-datetime-picker) {
  flex: 1;
  width: 100%;
}

.real-name {
  :deep(.wd-cell__wrapper) {
    padding: 30rpx 0 !important;
  }
}

.footer-fixed {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>
