<route lang="json5">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '基本信息',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="page-container flex flex-col p-30rpx">
    <view class="info-tip py-3 text-sm text-gray-600">请确保以下信息正确</view>

    <!-- 基本信息表单 -->
    <wd-cell-group border class="info-form pr-20rpx mb-4 rounded-20rpx overflow-hidden">
      <wd-cell title="学历" :value="formData.education" is-link @click="handleSelectEducation" />
      <wd-cell title="职业" :value="formData.occupation" is-link @click="handleSelectOccupation" />
      <wd-cell title="芝麻分" :value="formData.creditScore" is-link @click="handleSelectCredit" />
    </wd-cell-group>

    <!-- 公司信息表单 -->
    <wd-cell-group border class="info-form pr-20rpx mb-4 rounded-20rpx overflow-hidden">
      <wd-cell title="公司名称" :value="formData.companyName" is-link @click="handleCompanyName" />
      <wd-cell title="公司电话" :value="formData.companyPhone" is-link @click="handleCompanyPhone" />
      <wd-cell title="公司地址" :value="formData.companyAddress" is-link @click="handleCompanyAddress" />
    </wd-cell-group>

    <!-- 地址信息表单 -->
    <wd-cell-group border class="info-form pr-20rpx mb-4 rounded-20rpx overflow-hidden">
      <wd-cell title="所在地" :value="formData.region" is-link @click="handleSelectRegion" />
      <wd-cell title="家庭地址" :value="formData.homeAddress" is-link @click="handleHomeAddress" />
    </wd-cell-group>

    <!-- 底部按钮 -->
    <view class="mt-50rpx px-4 py-3">
      <button class="btn-primary h-96rpx lh-96rpx rounded-full" @click="handleNext">下一步</button>
    </view>
  </view>
</template>

<script setup lang="ts">
defineOptions({
  name: 'LoanInfoPage'
})

import { ref, reactive, onMounted } from 'vue'

// 表单数据
const formData = reactive({
  education: '',
  occupation: '',
  creditScore: '',
  companyName: '',
  companyPhone: '',
  companyAddress: '',
  region: '浙江省/杭州市/西湖区',
  homeAddress: ''
})

// 页面加载
onMounted(() => {
  // 可以从缓存或API加载已有数据
})

// 处理选择学历
const handleSelectEducation = () => {
  uni.showActionSheet({
    itemList: ['高中', '大专', '本科', '硕士', '博士'],
    success: res => {
      const educationList = ['高中', '大专', '本科', '硕士', '博士']
      formData.education = educationList[res.tapIndex]
    }
  })
}

// 处理选择职业
const handleSelectOccupation = () => {
  uni.showActionSheet({
    itemList: ['上班族', '公务员', '企业主', '自由职业', '学生'],
    success: res => {
      const occupationList = ['上班族', '公务员', '企业主', '自由职业', '学生']
      formData.occupation = occupationList[res.tapIndex]
    }
  })
}

// 处理选择芝麻分
const handleSelectCredit = () => {
  uni.showActionSheet({
    itemList: ['550以下', '550-600', '600-650', '650-700', '700以上'],
    success: res => {
      const creditList = ['550以下', '550-600', '600-650', '650-700', '700以上']
      formData.creditScore = creditList[res.tapIndex]
    }
  })
}

// 处理输入公司名称
const handleCompanyName = () => {
  uni.showModal({
    title: '公司名称',
    editable: true,
    placeholderText: '请输入公司名称',
    content: formData.companyName,
    success: res => {
      if (res.confirm && res.content) {
        formData.companyName = res.content
      }
    }
  })
}

// 处理输入公司电话
const handleCompanyPhone = () => {
  uni.showModal({
    title: '公司电话',
    editable: true,
    placeholderText: '请输入公司电话',
    content: formData.companyPhone,
    success: res => {
      if (res.confirm && res.content) {
        formData.companyPhone = res.content
      }
    }
  })
}

// 处理输入公司地址
const handleCompanyAddress = () => {
  uni.showModal({
    title: '公司地址',
    editable: true,
    placeholderText: '请输入公司地址',
    content: formData.companyAddress,
    success: res => {
      if (res.confirm && res.content) {
        formData.companyAddress = res.content
      }
    }
  })
}

// 处理选择地区
const handleSelectRegion = () => {
  // 实际项目中可能需要使用picker组件选择省市区
  uni.showToast({
    title: '请选择省市区',
    icon: 'none'
  })
}

// 处理输入家庭地址
const handleHomeAddress = () => {
  uni.showModal({
    title: '家庭地址',
    editable: true,
    placeholderText: '请输入家庭地址',
    content: formData.homeAddress,
    success: res => {
      if (res.confirm && res.content) {
        formData.homeAddress = res.content
      }
    }
  })
}

// 处理下一步
const handleNext = () => {
  // 表单验证
  if (
    !formData.education ||
    !formData.occupation ||
    !formData.creditScore ||
    !formData.companyName ||
    !formData.companyPhone ||
    !formData.companyAddress ||
    !formData.region ||
    !formData.homeAddress
  ) {
    uni.showToast({
      title: '请完善所有信息',
      icon: 'none'
    })
    return
  }

  // 存储表单数据并跳转到联系人页面
  uni.setStorageSync('loanBasicInfo', formData)
  uni.navigateTo({
    url: '/pages/loan/contact'
  })
}
</script>

<style lang="scss" scoped>
.page-container {
  padding-bottom: 80rpx;
}

.bottom-button {
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-form {
  :deep(.wd-cell__wrapper) {
    padding: 30rpx 0 !important;

    .wd-cell__left {
      flex: none !important;
      width: 160rpx !important;
    }
  }
}

/* #ifdef h5 */
:deep(.uni-modal__textarea) {
  min-height: 200rpx !important;
}
/* #endif */
</style>
