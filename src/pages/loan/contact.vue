<route lang="json5">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '联系人信息',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="page-container flex flex-col p-30rpx bg-gray-100">
    <view class="info-tip py-3 text-26rpx text-#666">请确保以下信息正确</view>

    <!-- 紧急联系人一 -->
    <view class="contact-card bg-white rounded-lg mb-4 p-4">
      <view class="flex justify-between items-center mb-3">
        <text class="text-28rpx text-#222">紧急联系人一</text>
        <view class="camera-icon flex-center" @click="selectContact(1)">
          <text class="iconfont icon-camera"></text>
        </view>
      </view>

      <!-- 姓名输入框 -->
      <view class="input-wrap mb-3 border-b border-gray-100">
        <input
          class="contact-input text-32rpx text-#222"
          type="text"
          placeholder="请输入联系人姓名"
          v-model="formData.contactName"
        />
      </view>

      <view class="flex justify-between items-center">
        <!-- 电话输入框 -->
        <view class="input-wrap flex-1">
          <input
            class="contact-input text-26rpx text-#666"
            type="text"
            :maxlength="11"
            placeholder="请输入联系人电话"
            v-model="formData.contactPhone"
          />
        </view>
        <view class="edit-btn" @click="handleRelationship">{{ formData.relationship || '亲属' }}</view>
      </view>
    </view>

    <!-- 紧急联系人二 -->
    <view class="contact-card bg-white rounded-lg mb-4 p-4">
      <view class="flex justify-between items-center mb-3">
        <text class="text-28rpx text-#222">紧急联系人二</text>
        <view class="camera-icon flex-center" @click="selectContact(2)">
          <text class="iconfont icon-camera"></text>
        </view>
      </view>

      <!-- 姓名输入框 -->
      <view class="input-wrap mb-3 border-b border-gray-100">
        <input
          class="contact-input text-32rpx text-#222"
          type="text"
          placeholder="请输入联系人姓名"
          v-model="formData.spareContactName"
        />
      </view>

      <view class="flex justify-between items-center">
        <!-- 电话输入框 -->
        <view class="input-wrap flex-1">
          <input
            class="contact-input text-26rpx text-#666"
            type="text"
            :maxlength="11"
            placeholder="请输入联系人电话"
            v-model="formData.spareContactPhone"
          />
        </view>
        <view class="edit-btn" @click="handleSpareRelationship">{{ formData.spareRelationship || '亲属' }}</view>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-box p-4 text-24rpx text-#999 mb-6">
      <text class="tips-title block mb-2 text-28rpx text-#222">温馨提示：</text>
      <text class="tips-content">
        填写紧急联系人用于还款提醒和通知，逾期将联系紧急联系人提醒您下次还款。紧急联系人不会必须提供联系人且名单不能是同住家庭并且是可联系到的人。
      </text>
    </view>

    <!-- 底部按钮 -->
    <view class="mt-4">
      <button class="submit-btn h-96rpx lh-96rpx rounded-full text-white" @click="handleSubmit">提交</button>
    </view>
  </view>
</template>

<script setup lang="ts">
defineOptions({
  name: 'LoanContactPage'
})

import { ref, reactive, onMounted } from 'vue'

// 表单数据
const formData = reactive({
  contactName: '',
  relationship: '',
  contactPhone: '',
  spareContactName: '',
  spareRelationship: '',
  spareContactPhone: ''
})

// 当前选择的联系人类型
const currentContactType = ref(0)

// 页面加载
onMounted(() => {
  // 可以从缓存或API加载已有数据
})

// 选择联系人
const selectContact = (type: number) => {
  currentContactType.value = type
  console.log('开始选择联系人，类型:', type)

  // #ifdef APP-PLUS
  if (uni.requireNativePlugin) {
    try {
      // 使用uniapp提供的方式打开通讯录
      uni.chooseContact({
        success: res => {
          console.log('选择联系人成功:', JSON.stringify(res))
          let name = res.displayName || ''
          let phone = res.phoneNumber || ''

          // 清理电话号码中的非数字字符
          phone = phone.replace(/\D/g, '')

          // 更新表单数据
          if (type === 1) {
            formData.contactName = name
            formData.contactPhone = phone
          } else {
            formData.spareContactName = name
            formData.spareContactPhone = phone
          }
        },
        fail: err => {
          console.error('选择联系人失败:', JSON.stringify(err))
          tryDirectContactSelection(type)
        }
      })
    } catch (error) {
      console.error('调用uni.chooseContact错误:', error)
      tryDirectContactSelection(type)
    }
  } else {
    tryDirectContactSelection(type)
  }
  // #endif

  // #ifdef H5
  promptForContactInfo(type)
  // #endif
}

// 尝试直接调用plus接口
const tryDirectContactSelection = (type: number) => {
  // #ifdef APP-PLUS
  try {
    console.log('尝试直接调用plus接口选择联系人')
    // @ts-ignore - 忽略TypeScript错误
    const ADDRESSBOOK_PHONE = plus.contacts.ADDRESSBOOK_PHONE

    // @ts-ignore - 忽略TypeScript错误
    plus.contacts.getAddressBook(
      ADDRESSBOOK_PHONE,
      addressbook => {
        console.log('获取通讯录对象成功')

        // 直接打开通讯录选择界面
        // @ts-ignore - 忽略TypeScript错误
        plus.nativeUI.actionSheet(
          {
            title: '请选择操作',
            cancel: '取消',
            buttons: [{ title: '从通讯录选择' }]
          },
          e => {
            if (e.index === 1) {
              // 用户选择了"从通讯录选择"
              openNativeContacts(type)
            } else {
              // 用户取消了操作，使用手动输入
              promptForContactInfo(type)
            }
          }
        )
      },
      e => {
        console.error('获取通讯录对象失败:', e.message)
        promptForContactInfo(type)
      }
    )
  } catch (error) {
    console.error('直接调用plus接口错误:', error)
    promptForContactInfo(type)
  }
  // #endif
}

// 打开原生通讯录
const openNativeContacts = (type: number) => {
  // #ifdef APP-PLUS
  try {
    if (plus.os.name.toLowerCase() === 'android') {
      // Android平台
      // 使用原生Android Intent打开通讯录
      const main = plus.android.runtimeMainActivity()
      const Intent = plus.android.importClass('android.content.Intent')
      const ContactsContract = plus.android.importClass('android.provider.ContactsContract')

      const intent = new plus.android.instanceObject(Intent)
      // @ts-ignore - 忽略TypeScript错误
      intent.setAction(Intent.ACTION_PICK)
      // @ts-ignore - 忽略TypeScript错误
      intent.setData(ContactsContract.Contacts.CONTENT_URI)

      // 监听返回结果
      plus.android.registerActivityResultHandler((requestCode, resultCode, data) => {
        if (resultCode === -1 && data) {
          // RESULT_OK = -1
          try {
            const resolver = main.getContentResolver()
            const uri = data.getData()
            const cursor = resolver.query(uri, null, null, null, null)

            if (cursor.moveToFirst()) {
              // @ts-ignore - 忽略TypeScript错误
              const idColumn = cursor.getColumnIndex(ContactsContract.Contacts._ID)
              const id = cursor.getString(idColumn)
              // @ts-ignore - 忽略TypeScript错误
              const nameColumn = cursor.getColumnIndex(ContactsContract.Contacts.DISPLAY_NAME)
              const name = cursor.getString(nameColumn)

              // 查询电话号码
              // @ts-ignore - 忽略TypeScript错误
              const phoneCursor = resolver.query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                null,
                // @ts-ignore - 忽略TypeScript错误
                ContactsContract.CommonDataKinds.Phone.CONTACT_ID + '=?',
                [id],
                null
              )

              let phone = ''
              if (phoneCursor.moveToFirst()) {
                // @ts-ignore - 忽略TypeScript错误
                const phoneColumn = phoneCursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                phone = phoneCursor.getString(phoneColumn)
                phone = phone.replace(/\D/g, '')
              }
              phoneCursor.close()

              // 更新表单数据
              if (type === 1) {
                formData.contactName = name
                formData.contactPhone = phone
              } else {
                formData.spareContactName = name
                formData.spareContactPhone = phone
              }
            }
            cursor.close()
          } catch (e) {
            console.error('处理联系人数据错误:', e)
          }
        }
      })

      // 启动Activity
      main.startActivityForResult(intent, 1)
    } else {
      // iOS平台或其他平台，使用系统菜单
      uni.showActionSheet({
        itemList: ['从通讯录选择', '手动输入'],
        success: res => {
          if (res.tapIndex === 0) {
            // 使用uni API
            uni.chooseContact({
              success: contactRes => {
                const name = contactRes.displayName || ''
                const phone = (contactRes.phoneNumber || '').replace(/\D/g, '')

                if (type === 1) {
                  formData.contactName = name
                  formData.contactPhone = phone
                } else {
                  formData.spareContactName = name
                  formData.spareContactPhone = phone
                }
              },
              fail: () => {
                promptForContactInfo(type)
              }
            })
          } else {
            promptForContactInfo(type)
          }
        },
        fail: () => {
          promptForContactInfo(type)
        }
      })
    }
  } catch (error) {
    console.error('打开原生通讯录错误:', error)
    promptForContactInfo(type)
  }
  // #endif
}

// 使用对话框让用户输入联系人信息
const promptForContactInfo = (type: number) => {
  // 首先输入姓名
  uni.showModal({
    title: '请输入联系人姓名',
    editable: true,
    placeholderText: '请输入姓名',
    success: res => {
      if (res.confirm && res.content) {
        // 保存姓名
        const name = res.content

        // 然后输入电话
        uni.showModal({
          title: '请输入联系人电话',
          editable: true,
          placeholderText: '请输入手机号码',
          success: phoneRes => {
            if (phoneRes.confirm && phoneRes.content) {
              // 保存电话
              const phone = phoneRes.content

              // 更新表单数据
              if (type === 1) {
                formData.contactName = name
                formData.contactPhone = phone
              } else {
                formData.spareContactName = name
                formData.spareContactPhone = phone
              }
            }
          }
        })
      }
    }
  })
}

// 处理选择联系人关系
const handleRelationship = () => {
  uni.showActionSheet({
    itemList: ['父母', '配偶', '子女', '兄弟姐妹', '朋友', '同事'],
    success: res => {
      const relationshipList = ['父母', '配偶', '子女', '兄弟姐妹', '朋友', '同事']
      formData.relationship = relationshipList[res.tapIndex]
    }
  })
}

// 处理选择备用联系人关系
const handleSpareRelationship = () => {
  uni.showActionSheet({
    itemList: ['父母', '配偶', '子女', '兄弟姐妹', '朋友', '同事'],
    success: res => {
      const relationshipList = ['父母', '配偶', '子女', '兄弟姐妹', '朋友', '同事']
      formData.spareRelationship = relationshipList[res.tapIndex]
    }
  })
}

// 处理提交
const handleSubmit = () => {
  // 表单验证
  if (!formData.contactName || !formData.contactPhone) {
    uni.showToast({
      title: '请完善联系人一信息',
      icon: 'none'
    })
    return
  }

  if (!formData.spareContactName || !formData.spareContactPhone) {
    uni.showToast({
      title: '请完善联系人二信息',
      icon: 'none'
    })
    return
  }

  if (!formData.relationship) {
    formData.relationship = '亲属'
  }

  if (!formData.spareRelationship) {
    formData.spareRelationship = '亲属'
  }

  // 存储表单数据并跳转到结果页面
  uni.setStorageSync('loanContactInfo', formData)

  // 显示加载中
  uni.showLoading({
    title: '提交中...'
  })

  // 模拟提交数据过程
  setTimeout(() => {
    uni.hideLoading()
    // 跳转到结果页面
    uni.navigateTo({
      url: '/pages/loan/result'
    })
  }, 1000)
}
</script>

<style lang="scss" scoped>
.page-container {
  padding-bottom: 80rpx;
}

.contact-card {
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.camera-icon {
  width: 40rpx;
  height: 40rpx;
  background: url('@/static/images/loan/contact_icon.png') no-repeat center center/contain;

  .iconfont {
    font-size: 36rpx;
    color: #666;
  }
}

.edit-btn {
  padding: 4rpx 16rpx;
  font-size: 20rpx;
  color: #fff;
  background-color: #ff4d4f;
  border-radius: 999rpx;
}

.submit-btn {
  font-size: 32rpx;
  font-weight: 500;
  background-color: #ff4d4f;
}

.tips-box {
  border-radius: 12rpx;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-wrap {
  padding: 10rpx 0;
}

.contact-input {
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
}
</style>
