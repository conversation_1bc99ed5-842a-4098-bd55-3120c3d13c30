<route lang="json5">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '审核结果',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="flex flex-col items-center min-h-screen bg-white">
    <!-- 图标 -->
    <view class="mb-5 my-20%">
      <wd-img width="160rpx" height="160rpx" mode="aspectFit" src="/static/images/loan/loan_result_icon.png" />
    </view>

    <!-- 状态文本 -->
    <view class="text-36rpx font-medium text-#333 mb-4">提交成功了，正在加速审核中</view>
    <view class="text-26rpx text-#999 mb-12 px-8 text-center">
      您的订单进入待审核阶段，审核人员将快速完成审核，请及时查看订单状态和短信提醒。
    </view>

    <!-- 刷新按钮 -->
    <view class="w-90% max-w-600rpx">
      <button class="refresh-btn h-96rpx lh-96rpx rounded-full text-#FF4D4F border-#FF4D4F" @click="goToHome">
        返回首页
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
defineOptions({
  name: 'LoanResultPage'
})

import { onMounted, ref } from 'vue'

// 状态标识
const status = ref('loading') // loading, success, failed

// 页面加载
onMounted(() => {
  checkStatus()
})

// 检查审核状态
const checkStatus = () => {
  // 这里应该调用实际的API检查审核状态
  // 示例：模拟API调用
  status.value = 'loading'

  // 演示用：模拟请求
  setTimeout(() => {
    // 假设状态没变，依然在审核中
    status.value = 'loading'
  }, 1000)
}

// 刷新状态
const refreshStatus = () => {
  uni.showLoading({
    title: '刷新中...'
  })

  // 模拟请求刷新状态
  setTimeout(() => {
    uni.hideLoading()
    checkStatus()

    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  }, 1500)
}

// 返回首页
const goToHome = () => {
  // 返回到首页Tab
  uni.switchTab({
    url: '/pages/tab/home/<USER>',
    success: () => {
      console.log('成功跳转到首页')
    },
    fail: err => {
      console.error('跳转失败:', err)
      // 如果switchTab失败，尝试使用reLaunch
      uni.reLaunch({
        url: '/pages/tab/home/<USER>'
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.refresh-btn {
  font-size: 32rpx;
  font-weight: 500;
  background-color: transparent;
  border: 2rpx solid #ff4d4f;
}
</style>
