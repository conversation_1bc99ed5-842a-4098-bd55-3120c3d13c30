<template>
  <!-- 花花卡组件容器 -->
  <view
    class="pos-relative z-100 mx-30rpx py-15rpx px-34rpx box-border h-226rpx justify-between rounded-xl shadow bg-[linear-gradient(119.95deg,_#FEFBEC_0%,_#FFFBE8_30.96%,_#F7DDA5_100%)]"
  >
    <!-- 骨架屏 - 数据加载时显示 -->
    <view v-if="isLoading" class="skeleton-container h-full">
      <view class="flex items-center mt-20rpx">
        <view class="skeleton-logo skeleton-animation"></view>
        <view class="skeleton-title skeleton-animation ml-15rpx"></view>
      </view>
      <view class="flex justify-between items-center mt-30rpx">
        <view class="skeleton-amount skeleton-animation"></view>
        <view class="skeleton-button skeleton-animation"></view>
      </view>
    </view>

    <!-- 状态1: 审核中 - 只显示标题和描述 -->
    <view v-else-if="cardData.cardStatus === CardStatus.PENDING">
      <view class="mt-30rpx">
        <!-- 将审核中文字替换为图片 -->
        <wd-img width="200rpx" height="48rpx" mode="aspectFit" src="/static/images/loan/card_loading.png" />
      </view>
      <view class="text-22rpx text-[#947641] mt-1">正在加速审核，请耐心等待...</view>
      <wd-img
        class="!pos-absolute right--50rpx z-10 top--50rpx"
        width="300rpx"
        mode="widthFix"
        src="/static/images/home/<USER>"
      />
    </view>

    <!-- 状态3: 授信失败 - 显示失败提示和重新申请按钮 -->
    <view v-else-if="cardData.cardStatus === CardStatus.REJECTED">
      <view class="mt-30rpx text-[#3C2913]">
        <wd-img width="160rpx" height="48rpx" mode="aspectFit" src="/static/images/loan/card_fail.png" />
      </view>
      <view class="text-22rpx text-[#947641] mt-1">您的首先不满足要求，请1个月后再来试试。</view>
      <view class="flex-1 flex justify-end">
        <view class="submit-btn">
          <button
            class="rounded-full text-white font-500 bg-[#3C2913] w-196rpx h-60rpx leading-60rpx text-center !text-28rpx relative z-100"
            @click="handleCardAction"
            :disabled="cardData.loading"
          >
            {{ cardData.loading ? '加载中...' : cardData.buttonText }}
          </button>
        </view>
      </view>
      <wd-img
        class="!pos-absolute right--50rpx z-10 top--50rpx"
        width="300rpx"
        mode="widthFix"
        src="/static/images/home/<USER>"
      />
    </view>

    <!-- 状态2: 已授信待还款 - 显示待还金额、到期日期和可用额度 -->
    <view v-else-if="cardData.cardStatus === CardStatus.APPROVED">
      <view>
        <view class="text-xs text-gray-500">
          <view class="flex items-center">
            <wd-img width="91rpx" height="37rpx" mode="widthFix" src="/static/images/home/<USER>" />
            <view class="text-22rpx h-22rpx ml-15rpx leading-37rpx text-[#947641]">
              {{ cardData.dueDate }} 待还（元）
            </view>
            <view
              class="pos-absolute right-0 top-0 text-22rpx ml-15rpx l text-[#574424] rounded-[0rpx_21.61rpx_0rpx_21.61rpx] bg-gradient-to-r from-[#BA8C5B] to-[#E0C5AA] w-145rpx h-28rpx text-center leading-[32rpx] !text-20rpx"
            >
              {{ cardData.badgeText }}
            </view>
          </view>
        </view>

        <!-- 待还款金额 -->
        <view class="pos-relative z-100 mt-10rpx flex items-center">
          <view class="text-76rpx text-[#3C2913] font-bold">{{ cardData.dueAmount }}</view>
          <view class="flex-1 flex justify-end">
            <view class="submit-btn !bottom--35rpx !right-20rpx !absolute">
              <button
                class="rounded-full text-white font-500 bg-[#3C2913] w-196rpx h-60rpx leading-60rpx text-center !text-28rpx"
                @click="handleCardAction"
              >
                账单明细
              </button>
            </view>
          </view>
        </view>

        <!-- 可用额度展示 -->
        <view class="text-22rpx leading-37rpx text-[#947641]">可用额度（元）: {{ cardData.availableAmount }}</view>
      </view>
      <wd-img
        class="!pos-absolute right--50rpx z-10 top--50rpx"
        width="300rpx"
        mode="widthFix"
        src="/static/images/home/<USER>"
      />
    </view>

    <!-- 状态4: 默认状态 - 显示最高分期额度和立即申请按钮 -->
    <view v-else>
      <view>
        <view class="text-xs text-gray-500">
          <view class="flex items-center">
            <wd-img width="91rpx" height="37rpx" mode="widthFix" src="/static/images/home/<USER>" />
            <view class="text-22rpx h-22rpx ml-15rpx leading-37rpx text-[#947641]">
              {{ cardData.title }}
            </view>
            <view
              v-if="cardData.showBadge"
              class="pos-absolute right-0 top-0 text-22rpx ml-15rpx l text-[#574424] rounded-[0rpx_21.61rpx_0rpx_21.61rpx] bg-gradient-to-r from-[#BA8C5B] to-[#E0C5AA] w-145rpx h-28rpx text-center leading-[32rpx] !text-20rpx"
            >
              {{ cardData.badgeText }}
            </view>
          </view>
        </view>
        <view class="pos-relative z-100 mt-10rpx flex items-center">
          <wd-img width="61rpx" mode="widthFix" src="/static/images/home/<USER>" />
          <view class="text-90rpx text-[#3C2913] font-bold ml-22rpx">{{ cardData.amount }}</view>

          <view class="flex-1 flex justify-end">
            <view class="submit-btn !bottom--15rpx !right-25rpx !absolute">
              <button
                class="rounded-full text-white font-500 bg-[#3C2913] w-196rpx h-60rpx leading-60rpx text-center !text-28rpx"
                @click="handleCardAction"
              >
                {{ cardData.loading ? '加载中...' : cardData.buttonText }}
              </button>
            </view>
          </view>
        </view>
      </view>
      <wd-img
        class="!pos-absolute right--50rpx z-10 top--50rpx"
        width="300rpx"
        mode="widthFix"
        src="/static/images/home/<USER>"
      />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'

/**
 * 花花卡组件状态枚举
 */
enum CardStatus {
  NORMAL = 'normal', // 默认状态 - 显示立即申请
  PENDING = 'pending', // 审核中状态
  APPROVED = 'approved', // 已授信状态 - 显示待还款
  REJECTED = 'rejected' // 授信失败状态
}

/**
 * 花花卡组件数据接口
 * @description 定义了花花卡组件的所有状态和数据
 */
interface CardData {
  // 显示的额度金额
  amount: string | number
  // 卡片标题
  title: string
  // 按钮文本
  buttonText: string
  // 是否显示角标
  showBadge: boolean
  // 角标文本
  badgeText: string
  // 加载状态
  loading: boolean
  // 上次更新时间
  lastUpdateTime: string
  // 卡片状态
  cardStatus: CardStatus
  // 到期日期（已授信状态显示）
  dueDate?: string
  // 待还款金额（已授信状态显示）
  dueAmount?: string | number
  // 可用额度（已授信状态显示）
  availableAmount?: string | number
}

/**
 * 花花卡状态配置
 * 集中管理不同状态下的UI和行为配置
 */
const cardStatusConfig = {
  [CardStatus.NORMAL]: {
    buttonText: '立即申请',
    badgeText: '先享后付',
    action: 'navigateToAuth'
  },
  [CardStatus.PENDING]: {
    buttonText: '审核中',
    badgeText: '审核中',
    action: 'none'
  },
  [CardStatus.APPROVED]: {
    buttonText: '账单明细',
    badgeText: '已开通',
    action: 'viewBill'
  },
  [CardStatus.REJECTED]: {
    buttonText: '重新申请',
    badgeText: '申请失败',
    action: 'reapply'
  }
}

// 默认数据
const defaultCardData: CardData = {
  amount: '50000',
  title: '商城最高分期额度（元）',
  buttonText: '立即申请',
  showBadge: true,
  badgeText: '先享后付',
  loading: false,
  lastUpdateTime: '',
  cardStatus: CardStatus.NORMAL,
  dueDate: '',
  dueAmount: '0',
  availableAmount: '0'
}

// 骨架屏加载状态
const isLoading = ref(true)

// 组件内部状态
const cardData = reactive<CardData>({ ...defaultCardData })

/**
 * 刷新花花卡数据
 * 从服务器获取最新的卡片状态和数据
 */
const refresh = async () => {
  isLoading.value = true
  cardData.loading = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟获取新数据
    const mockData = await fetchCardData()
    Object.assign(cardData, mockData)

    // 更新最后刷新时间
    cardData.lastUpdateTime = new Date().toLocaleString()

    console.log('花花卡数据已刷新')
  } catch (error) {
    console.error('刷新花花卡数据失败:', error)
    uni.showToast({
      title: '刷新失败，请重试',
      icon: 'none'
    })
  } finally {
    cardData.loading = false
    isLoading.value = false
  }
}

/**
 * 模拟获取卡片数据的API
 * 在实际项目中，这里应该调用真实的API接口
 *
 * @returns {Promise<Partial<CardData>>} 返回部分卡片数据
 */
const fetchCardData = async (): Promise<Partial<CardData>> => {
  // 这里可以替换为真实的API调用
  return new Promise(resolve => {
    setTimeout(() => {
      // 模拟不同的数据状态
      const randomStatus = 1 || Math.random()
      let status: CardStatus = CardStatus.NORMAL
      let amount = '50000'
      let dueDate = ''
      let dueAmount = '0'
      let availableAmount = '0'

      if (randomStatus < 0.3) {
        // 状态1: 审核中
        status = CardStatus.PENDING
        amount = '30000'
      } else if (randomStatus < 0.6) {
        // 状态2: 已授信，显示待还款
        status = CardStatus.APPROVED
        amount = '80000'
        dueDate = '06月18日到期'
        dueAmount = '8806.56'
        availableAmount = '21345'
      } else if (randomStatus < 0.8) {
        // 状态3: 授信失败
        status = CardStatus.REJECTED
        amount = '0'
      } else {
        // 状态4: 默认状态，未申请
        status = CardStatus.NORMAL
        amount = '50000'
      }

      // 根据状态获取对应的配置
      const config = cardStatusConfig[status]

      resolve({
        amount,
        buttonText: config.buttonText,
        badgeText: config.badgeText,
        cardStatus: status,
        dueDate,
        dueAmount,
        availableAmount
      })
    }, 500)
  })
}

/**
 * 处理卡片操作
 * 根据当前卡片状态执行不同的操作
 */
const handleCardAction = () => {
  if (cardData.loading) return

  const action = cardStatusConfig[cardData.cardStatus]?.action

  switch (action) {
    case 'navigateToAuth':
      navigateToAuth()
      break
    case 'viewBill':
      viewBill()
      break
    case 'reapply':
      reapply()
      break
    default:
      // 默认不执行任何操作
      break
  }
}

/**
 * 导航到授权页面
 * 当卡片处于默认状态时，点击按钮跳转到授权页面
 */
const navigateToAuth = () => {
  uni.navigateTo({
    url: '/pages/loan/auth'
  })
  emit('navigate', { page: 'auth' })
}

/**
 * 查看账单明细
 * 已授信状态下可点击查看账单
 */
const viewBill = () => {
  uni.navigateTo({
    url: '/pages/user/bill-detail'
  })
  emit('viewBill')
}

/**
 * 重新申请
 * 授信失败状态下可重新申请
 */
const reapply = async () => {
  cardData.loading = true
  try {
    // 模拟申请流程
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新状态为审核中
    cardData.cardStatus = CardStatus.PENDING
    cardData.buttonText = cardStatusConfig[CardStatus.PENDING].buttonText
    cardData.badgeText = cardStatusConfig[CardStatus.PENDING].badgeText

    uni.showToast({
      title: '申请已提交，请等待审核',
      icon: 'success'
    })

    // 通知父组件数据变化
    emit('dataChange', { ...cardData })
  } catch (error) {
    uni.showToast({
      title: '申请失败，请重试',
      icon: 'none'
    })
  } finally {
    cardData.loading = false
  }
}

/**
 * 组件事件定义
 */
interface Emits {
  // 数据变更事件
  (e: 'dataChange', data: CardData): void
  // 刷新事件
  (e: 'refresh'): void
  // 查看账单事件
  (e: 'viewBill'): void
  // 导航事件
  (e: 'navigate', data: { page: string }): void
}

const emit = defineEmits<Emits>()

// 组件初始化
onMounted(async () => {
  await refresh()
})

// 监听数据变化，通知父组件
watch(
  cardData,
  newData => {
    emit('dataChange', { ...newData })
  },
  { deep: true }
)

/**
 * 暴露方法给父组件
 */
defineExpose({
  refresh,
  getData: () => ({ ...cardData }),
  getAmount: () => cardData.amount,
  getStatus: () => cardData.cardStatus,
  getLastUpdateTime: () => cardData.lastUpdateTime,
  // 新增方法
  setCardStatus: (status: CardStatus) => {
    if (Object.values(CardStatus).includes(status)) {
      cardData.cardStatus = status
      const config = cardStatusConfig[status]
      cardData.buttonText = config.buttonText
      cardData.badgeText = config.badgeText
    }
  }
})
</script>

<style lang="scss" scoped>
.submit-btn {
  position: relative;
  bottom: 0;

  &::before {
    position: absolute;
    top: -30rpx;
    left: 10rpx;
    z-index: 100;
    width: 80rpx;
    height: 30rpx;
    content: '';
    background: url('/static/images/home/<USER>') no-repeat center center/cover;
  }
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 骨架屏样式 */
.skeleton-container {
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx 0;
}

.skeleton-animation {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-logo {
  width: 91rpx;
  height: 37rpx;
}

.skeleton-title {
  width: 180rpx;
  height: 22rpx;
}

.skeleton-amount {
  width: 180rpx;
  height: 70rpx;
}

.skeleton-button {
  width: 150rpx;
  height: 50rpx;
  border-radius: 30rpx;
}

.skeleton-available {
  display: none;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
