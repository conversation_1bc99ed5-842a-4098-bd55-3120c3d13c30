<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useToast } from 'wot-design-uni'
import { useGlobalLoading } from '@/hooks/useGlobalLoading'

// 获取当前页面路径
function getCurrentPath(): string {
  const pages = getCurrentPages()
  return pages.length > 0 ? pages[pages.length - 1].route || '' : ''
}

const { loadingOptions, currentPage } = storeToRefs(useGlobalLoading())

const { close: closeGlobalLoading } = useGlobalLoading()

const loading = useToast('globalLoading')
const currentPath = getCurrentPath()

// #ifdef MP-ALIPAY
const hackAlipayVisible = ref(false)

nextTick(() => {
  hackAlipayVisible.value = true
})
// #endif

watch(
  () => loadingOptions.value,
  newVal => {
    if (newVal && newVal.show) {
      if (currentPage.value === currentPath) {
        loading.loading(loadingOptions.value)
      }
    } else {
      loading.close()
    }
  }
)
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared'
  }
}
</script>

<template>
  <!-- #ifdef MP-ALIPAY -->
  <wd-toast v-if="hackAlipayVisible" selector="globalLoading" :closed="closeGlobalLoading" />
  <!-- #endif -->
  <!-- #ifndef MP-ALIPAY -->
  <wd-toast selector="globalLoading" :closed="closeGlobalLoading" />
  <!-- #endif -->
</template>
