<template>
  <view class="search-box" @click.stop>
    <!-- 搜索输入插槽，暴露 v-model、loading、onSearch、focus状态 -->
    <slot
      name="input"
      :model-value="inputValue"
      :loading="loading"
      :on-search="handleSearch"
      :on-input="handleInput"
      :on-focus="handleFocus"
      :on-blur="handleBlur"
      :is-focused="isFocused"
    >
      <!-- 默认输入框 -->
      <input
        v-model="inputValue"
        :placeholder="placeholder"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keyup.enter="handleSearch"
        class="search-input"
      />
    </slot>

    <!-- 结果下拉插槽，暴露结果数据、loading、选中项、onSelect -->
    <transition
      name="search-dropdown"
      enter-active-class="search-dropdown-enter-active"
      leave-active-class="search-dropdown-leave-active"
      enter-from-class="search-dropdown-enter-from"
      leave-to-class="search-dropdown-leave-to"
    >
      <view v-if="showDropdown && (results.length > 0 || isFocused)" class="search-dropdown">
        <slot
          name="results"
          :results="results"
          :loading="loading"
          :select="handleSelect"
          :input-value="inputValue"
          :is-focused="isFocused"
        >
          <!-- 默认结果列表 -->
          <view v-for="item in results" :key="item.id" @click="handleSelect(item)" class="search-result-item">
            {{ item.title || item.name || item.keyword || item.id }}
          </view>
          <!-- 无结果时显示提示 -->
          <wd-status-tip
            v-if="inputValue.trim().length > 0 && !loading && results.length === 0"
            image="content"
            tip="无匹配结果"
            image-size="120rpx"
            class="search-no-result"
          />
        </slot>
      </view>
    </transition>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { debounce } from '@/utils'

interface Props {
  placeholder?: string
  searchApi: (keyword: string) => Promise<any[]>
  debounceMs?: number
  autoSearch?: boolean
  modelValue?: string
}
const props = defineProps<Props>()
const emit = defineEmits(['select', 'search', 'update:modelValue', 'focus', 'blur'])

const inputValue = ref('')
const loading = ref(false)
const results = ref<any[]>([])
const showDropdown = ref(false)
const isFocused = ref(false)
let blurTimer: number | null = null
let lastKeyword = ''

// 防抖搜索
const doSearch = debounce(async (keyword: string) => {
  if (!keyword.trim()) {
    results.value = []
    loading.value = false
    return
  }
  lastKeyword = keyword
  loading.value = true
  try {
    const list = await props.searchApi(keyword)
    // 只处理当前输入的结果
    if (lastKeyword === inputValue.value) {
      results.value = list || []
    }
  } finally {
    if (lastKeyword === inputValue.value) {
      loading.value = false
    }
  }
}, props.debounceMs || 300)

// 直接搜索（不使用防抖）
const searchImmediately = async (keyword: string) => {
  if (!keyword.trim()) {
    results.value = []
    loading.value = false
    return
  }
  lastKeyword = keyword
  loading.value = true
  try {
    const list = await props.searchApi(keyword)
    if (lastKeyword === inputValue.value) {
      results.value = list || []
    }
    showDropdown.value = true
  } finally {
    if (lastKeyword === inputValue.value) {
      loading.value = false
    }
  }
}

function handleInput(e: any) {
  inputValue.value = typeof e === 'string' ? e : e?.target?.value || ''
  emit('update:modelValue', inputValue.value)
  if (props.autoSearch !== false) doSearch(inputValue.value)
}

function handleSearch() {
  emit('search', inputValue.value)
  doSearch(inputValue.value)
}

function handleSelect(item: any) {
  emit('select', item)
  closeDropdown()
}

function handleFocus() {
  isFocused.value = true
  showDropdown.value = true
  emit('focus')

  // 如果聚焦时有输入内容，直接搜索并展示结果
  if (inputValue.value.trim()) {
    searchImmediately(inputValue.value)
  }
}

function handleBlur() {
  isFocused.value = false
  emit('blur')
  // 延迟关闭下拉，避免点击结果项时立即关闭
  if (blurTimer) clearTimeout(blurTimer)
  blurTimer = setTimeout(() => {
    if (!isFocused.value) {
      closeDropdown()
    }
  }, 200)
}

function closeDropdown() {
  showDropdown.value = false
  results.value = []
}

// 点击外部关闭下拉
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement
  if (!target.closest('.search-box')) {
    isFocused.value = false
    closeDropdown()
  }
}

// 外部可控
watch(
  () => props.modelValue,
  v => {
    inputValue.value = v || ''
  }
)

// 监听全局点击
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  if (blurTimer) clearTimeout(blurTimer)
})
</script>

<style scoped>
.search-box {
  position: relative;
}
.search-input {
  width: 100%;
  height: 40rpx;
  padding: 0 16rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
}
.search-dropdown {
  position: absolute;
  top: 110%;
  right: 0;
  left: 0;
  z-index: 9999;
  overflow: hidden;
  background: #fff;
  border: none;
  border-radius: 18rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.12),
    0 1.5rpx 6rpx rgba(0, 0, 0, 0.04);
  transform-origin: top center;
}
.search-result-item {
  padding: 20rpx 28rpx;
  font-size: 18rpx;
  color: #222;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s;
  animation: slideIn 0.2s ease-out;
}
.search-result-item:hover {
  color: #f6312d;
  background: #ffe5db;
}
.search-no-result {
  padding: 20rpx 28rpx;
  font-size: 18rpx;
  color: #999;
  text-align: center;
}

/* 下拉动画 */
.search-dropdown-enter-active,
.search-dropdown-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-dropdown-enter-from {
  opacity: 0;
  transform: translateY(-10rpx) scale(0.95);
}

.search-dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10rpx) scale(0.95);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
