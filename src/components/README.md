# 公共组件文档

本文档记录了项目中所有公共组件的使用方法和API。

## 📁 组件列表

### 1. CardQuota 花花卡额度组件

一个自主管理数据的花花卡额度展示组件，支持动态刷新和数据获取。

#### 基本使用

```vue
<template>
  <card-quota ref="cardQuotaRef" @data-change="handleDataChange" />
</template>

<script setup>
import CardQuota from '@/components/card-quota.vue'
import { ref } from 'vue'

const cardQuotaRef = ref()

// 处理数据变化
const handleDataChange = data => {
  console.log('卡片数据更新:', data)
}
</script>
```

#### API

**组件引用方法：**

```typescript
// 刷新数据
await cardQuotaRef.value?.refresh()

// 获取完整数据
const data = cardQuotaRef.value?.getData()

// 获取额度金额
const amount = cardQuotaRef.value?.getAmount()

// 获取卡片状态
const status = cardQuotaRef.value?.getStatus()

// 获取最后更新时间
const updateTime = cardQuotaRef.value?.getLastUpdateTime()
```

**事件：**

```typescript
// 数据变化事件
@data-change="handleDataChange"
// 参数: CardData 完整的卡片数据
```

**数据接口：**

```typescript
interface CardData {
  amount: string | number // 额度金额
  title: string // 标题文本
  buttonText: string // 按钮文本
  showBadge: boolean // 是否显示徽章
  badgeText: string // 徽章文本
  loading: boolean // 加载状态
  lastUpdateTime: string // 最后更新时间
  cardStatus: 'normal' | 'pending' | 'approved' | 'rejected' // 卡片状态
}
```

#### 特性

- ✅ 自主管理数据，无需父组件传入
- ✅ 支持手动刷新数据
- ✅ 自动处理加载状态
- ✅ 支持多种卡片状态
- ✅ 数据变化时自动通知父组件
- ✅ 完整的错误处理

#### 使用场景

- 首页花花卡额度展示
- 个人中心额度信息
- 申请页面额度预览
- 任何需要展示额度信息的地方

---

### 2. GoodsItem 商品项组件

商品展示组件，用于在列表中展示商品信息，支持添加到购物车功能。

#### 基本使用

```vue
<template>
  <goods-item :goods="goodsData" />
</template>

<script setup>
import GoodsItem from '@/components/goods-item.vue'

const goodsData = {
  id: 1,
  image: '/static/images/goods.png',
  title: '商品名称',
  price: '99.00',
  sold: '1000+'
}
</script>
```

#### API

**Props：**

```typescript
interface Goods {
  id?: number | string // 商品ID（可选）
  image: string // 商品图片
  title: string // 商品标题
  price: string | number // 商品价格
  sold: string | number // 销量信息
}
```

**事件：**

```typescript
// 添加到购物车事件（内部触发）
// 通过 uni.$emit('add-to-cart') 发送全局事件
```

#### 特性

- ✅ 商品图片展示
- ✅ 商品标题和价格显示
- ✅ 销量信息展示
- ✅ 添加到购物车功能
- ✅ 响应式布局

#### 使用场景

- 商品列表展示
- 商品推荐
- 搜索结果
- 购物车商品展示

---

### 3. ShopTitle 商城标题组件

商城页面标题组件，用于展示页面标题，具有特殊的装饰样式。

#### 基本使用

```vue
<template>
  <shop-title title="商品推荐" />
</template>

<script setup>
import ShopTitle from '@/components/shop-title.vue'
</script>
```

#### API

**Props：**

```typescript
interface Props {
  title: string // 标题文本（必填）
}
```

#### 特性

- ✅ 居中显示标题
- ✅ 左右装饰线条
- ✅ 渐变圆形装饰
- ✅ 响应式设计

#### 使用场景

- 商城页面标题
- 商品分类标题
- 活动页面标题
- 任何需要装饰性标题的地方

---

## 🎯 组件开发规范

### 1. 组件命名

- 使用 PascalCase 命名组件文件
- 组件名应该具有描述性，清晰表达功能

### 2. 文件结构

```
src/components/
├── component-name.vue          # 组件文件
├── component-name.types.ts     # 类型定义（可选）
└── README.md                   # 组件文档
```

### 3. 组件设计原则

- **单一职责**：每个组件只负责一个功能
- **可复用性**：组件应该可以在多个地方使用
- **可配置性**：通过props提供配置选项
- **事件通信**：通过事件与父组件通信
- **数据管理**：组件内部管理自己的状态

### 4. 代码规范

- 使用 TypeScript 定义接口
- 使用 Composition API
- 提供完整的类型定义
- 添加必要的注释
- 处理错误情况

### 5. 文档要求

每个组件都应该包含：

- 基本使用示例
- 完整的API文档
- Props和Events说明
- 使用场景描述
- 注意事项

---

## 📝 更新日志

### v1.0.0 (2024-01-XX)

- ✨ 新增 CardQuota 花花卡额度组件
- ✨ 新增 GoodsItem 商品项组件
- ✨ 新增 ShopTitle 商城标题组件
- 📚 完善组件文档

---

## 🤝 贡献指南

1. 创建新组件时，请遵循上述规范
2. 更新组件时，请同步更新文档
3. 提交代码前，请确保组件可以正常工作
4. 如有疑问，请联系开发团队

---

## 📞 联系方式

如有问题或建议，请联系：

- 开发团队：[<EMAIL>]
- 项目仓库：[GitHub Repository]
