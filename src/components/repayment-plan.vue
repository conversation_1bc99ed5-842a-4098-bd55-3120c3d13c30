<template>
  <view class="repayment-plan-component">
    <!-- 标题 -->
    <view class="text-32rpx font-500">还款计划</view>

    <!-- 内容区域 -->
    <view class="plan-content mt-16rpx">
      <!-- 总金额和费率信息 -->
      <view class="plan-total pb-30rpx border-b-2rpx-#F0F0F0">
        <view class="plan-amount mb-16rpx text-16rpx">
          还款总金额：
          <text class="text-20rpx font-500">¥{{ totalAmount }}</text>
        </view>
        <view class="plan-rate text-16rpx">
          年化综合资金利率：
          <text class="text-20rpx font-500">{{ annualRate }}%</text>
        </view>
      </view>

      <!-- 还款计划列表（可滚动区域） -->
      <view class="plan-list mt-30rpx overflow-y-auto" :style="{ maxHeight: maxHeight }">
        <view class="px-20rpx py-10rpx">
          <view
            class="relative mb-50rpx flex"
            v-for="(item, index) in planDetails"
            :key="index"
            :class="{ 'mb-0': index === planDetails.length - 1 }"
          >
            <!-- 左侧期数列 -->
            <view class="w-110rpx flex-shrink-0 text-right">
              <view class="mb-10rpx text-32rpx font-bold">第{{ index + 1 }}期</view>
              <view class="text-24rpx text-#666">{{ item.date }}</view>
            </view>

            <!-- 中间时间线列 -->
            <view class="relative mx-20rpx w-30rpx flex-shrink-0">
              <view class="timeline-dot absolute left-7rpx top-8rpx"></view>
              <view
                v-if="index !== planDetails.length - 1"
                class="absolute left-14rpx top-24rpx w-2rpx bg-#f0f0f0"
                style="height: calc(100% + 20rpx)"
              ></view>
            </view>

            <!-- 右侧详情列 -->
            <view class="flex-1">
              <view class="mb-10rpx text-32rpx text-#222 font-500">{{ item.amount.toFixed(2) }}</view>
              <view class="text-24rpx text-#666">
                本金{{ item.principal.toFixed(2) }} + 利息{{ item.interest.toFixed(2) }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
defineOptions({
  name: 'RepaymentPlan'
})

// 定义类型
interface RepaymentDetail {
  date: string
  amount: number
  principal: number
  interest: number
}

// 定义组件属性
const props = defineProps({
  planDetails: {
    type: Array as () => RepaymentDetail[],
    required: true
  },
  totalAmount: {
    type: Number,
    required: true
  },
  annualRate: {
    type: Number,
    required: true
  },
  maxHeight: {
    type: String,
    default: '50vh'
  }
})
</script>

<style lang="scss" scoped>
/* 红色空心圆点 */
.timeline-dot {
  z-index: 2;
  width: 16rpx;
  height: 16rpx;
  background-color: transparent;
  border: 2rpx solid #ff4d4f;
  border-radius: 50%;
}
</style>
