<template>
  <view class="shop-title">
    <view class="shop-title-line">
      <view class="shop-title-circle-left"></view>
    </view>
    <view class="shop-title-text">{{ title }}</view>
    <view class="shop-title-line ml-30rpx transform-rotate-180">
      <view class="shop-title-circle-right"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'ShopTitle'
})

defineProps<{
  title: string
}>()
</script>

<style lang="scss" scoped>
.shop-title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 55rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.shop-title-text {
  position: relative;
  z-index: 9;
}

.shop-title-line {
  position: relative;
  width: 100rpx;
  height: 100%;
  margin-right: 30rpx;
  background: url('/static/images/title-bg-line.png') no-repeat center center/contain;

  .shop-title-circle-left {
    position: absolute;
    right: -20rpx;
    bottom: 0;
    z-index: 1;
    width: 19.55rpx;
    height: 19.81rpx;
    background: linear-gradient(225deg, #fff 0%, #fa3411 100%);
    border-radius: 50%;
    opacity: 0.6;
  }

  .shop-title-circle-right {
    position: absolute;
    bottom: 0;
    left: 102rpx;
    z-index: -1;
    width: 42.66rpx;
    height: 43.23rpx;
    background: linear-gradient(45deg, #fa3411 0%, #fff 100%);
    border-radius: 50%;
    opacity: 0.6;
  }
}
</style>
