{
    "name" : "uni-plus",
    "appid" : "__UNI__18702EE",
    "description" : "uni-plus app 打包",
    "versionName" : "1.0.1",
    "versionCode" : 101,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true
        },
        /* 模块配置 */
        "modules" : {
            "Camera" : {},
            "Contacts" : {},
            "FaceID" : {},
            "FacialRecognitionVerify" : {},
            "Geolocation" : {},
            "Payment" : {}
        },
        "darkmode" : true, // 开启支持深色模式
        "themeLocation" : "theme.json", // 深色模式JSON文件，如果 theme.json 在根目录可省略

        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "arm64-v8a", "x86" ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "payment" : {}
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "postcss" : true,
            "minified" : true,
            "uglifyFileName" : true,
            "swc" : true
        },
        "usingComponents" : true,
        "darkmode" : true, // 开启支持深色模式
        "themeLocation" : "theme.json", // 深色模式JSON文件，如果 theme.json 在根目录可省略
        "libVersion" : "latest", // 处理微信开发者工具报错

        /* 该配置，上传时会忽略已配置的静态资源文件 */
        "packOptions" : {
            "ignore" : [
                {
                    "type" : "folder",
                    "value" : "static/onLine"
                }
            ]
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "locale" : "zh-Hans",
    "h5" : {}
}
// "router" : {
//     "base" : "/uni-plus/"
// }

