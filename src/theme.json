{
  // 浅色模式
  "light": {
    "navBgColor": "#fff", // 导航栏背景色
    "navTxtStyle": "black", // 导航栏文字颜色
    "bgColor": "#f5f5f5", // 页面背景色
    "bgTxtStyle": "light", // 下拉 loading 的样式，仅支持 dark/light
    "bgColorTop": "#eeeeee", // 顶部窗口的背景色（bounce回弹区域）
    "bgColorBottom": "#efefef", // 底部窗口的背景色（bounce回弹区域）
    "tabFontColor": "#666666", // tabBar 上的文字默认颜色
    "tabSelectedColor": "#FF3C29", // tabBar 上的文字选中颜色
    "tabBgColor": "#ffffff", // tabBar 背景色
    "tabBorderStyle": "white" // tabBar 上边框的颜色，可选值 black/white，也支持其他颜色值
  },
  // 深色模式
  "dark": {
    "navBgColor": "#1d1d1d", // 导航栏背景色
    "navTxtStyle": "white", // 导航栏文字颜色
    "bgColor": "#222222", // 页面背景色
    "bgTxtStyle": "dark", // 下拉 loading 的样式，仅支持 dark/light
    "bgColorTop": "#1e1e1e", // 顶部窗口的背景色（bounce回弹区域）
    "bgColorBottom": "#1e1e1e", // 底部窗口的背景色（bounce回弹区域）
    "tabFontColor": "#bbb", // tabBar 上的文字默认颜色
    "tabSelectedColor": "#FF3C29", // tabBar 上的文字选中颜色
    "tabBgColor": "#212121", // tabBar 背景色
    "tabBorderStyle": "black" // tabBar 上边框的颜色，可选值 black/white，也支持其他颜色值
  }
}
