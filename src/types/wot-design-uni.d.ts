/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// wot-design-uni组件库类型声明

import 'wot-design-uni/global'

declare module 'vue' {
  export interface GlobalComponents {
    // 基础组件
    'wd-badge': (typeof import('wot-design-uni/components/wd-badge/index'))['default']
    'wd-button': (typeof import('wot-design-uni/components/wd-button/index'))['default']
    'wd-config-provider': (typeof import('wot-design-uni/components/wd-config-provider/index'))['default']
    'wd-icon': (typeof import('wot-design-uni/components/wd-icon/index'))['default']
    'wd-loading': (typeof import('wot-design-uni/components/wd-loading/index'))['default']
    'wd-popup': (typeof import('wot-design-uni/components/wd-popup/index'))['default']
    'wd-transition': (typeof import('wot-design-uni/components/wd-transition/index'))['default']

    // 表单组件
    'wd-calendar': (typeof import('wot-design-uni/components/wd-calendar/index'))['default']
    'wd-calendar-view': (typeof import('wot-design-uni/components/wd-calendar-view/index'))['default']
    'wd-checkbox': (typeof import('wot-design-uni/components/wd-checkbox/index'))['default']
    'wd-checkbox-group': (typeof import('wot-design-uni/components/wd-checkbox-group/index'))['default']
    'wd-col-picker': (typeof import('wot-design-uni/components/wd-col-picker/index'))['default']
    'wd-datetime-picker': (typeof import('wot-design-uni/components/wd-datetime-picker/index'))['default']
    'wd-input': (typeof import('wot-design-uni/components/wd-input/index'))['default']
    'wd-picker': (typeof import('wot-design-uni/components/wd-picker/index'))['default']
    'wd-radio': (typeof import('wot-design-uni/components/wd-radio/index'))['default']
    'wd-radio-group': (typeof import('wot-design-uni/components/wd-radio-group/index'))['default']
    'wd-rate': (typeof import('wot-design-uni/components/wd-rate/index'))['default']
    'wd-search': (typeof import('wot-design-uni/components/wd-search/index'))['default']
    'wd-slider': (typeof import('wot-design-uni/components/wd-slider/index'))['default']
    'wd-switch': (typeof import('wot-design-uni/components/wd-switch/index'))['default']
    'wd-upload': (typeof import('wot-design-uni/components/wd-upload/index'))['default']

    // 反馈组件
    'wd-action-sheet': (typeof import('wot-design-uni/components/wd-action-sheet/index'))['default']
    'wd-dialog': (typeof import('wot-design-uni/components/wd-dialog/index'))['default']
    'wd-drop-menu': (typeof import('wot-design-uni/components/wd-drop-menu/index'))['default']
    'wd-drop-menu-item': (typeof import('wot-design-uni/components/wd-drop-menu-item/index'))['default']
    'wd-message-box': (typeof import('wot-design-uni/components/wd-message-box/index'))['default']
    'wd-notify': (typeof import('wot-design-uni/components/wd-notify/index'))['default']
    'wd-swipe-action': (typeof import('wot-design-uni/components/wd-swipe-action/index'))['default']
    'wd-toast': (typeof import('wot-design-uni/components/wd-toast/index'))['default']

    // 展示组件
    'wd-cell': (typeof import('wot-design-uni/components/wd-cell/index'))['default']
    'wd-cell-group': (typeof import('wot-design-uni/components/wd-cell-group/index'))['default']
    'wd-collapse': (typeof import('wot-design-uni/components/wd-collapse/index'))['default']
    'wd-collapse-item': (typeof import('wot-design-uni/components/wd-collapse-item/index'))['default']
    'wd-divider': (typeof import('wot-design-uni/components/wd-divider/index'))['default']
    'wd-img': (typeof import('wot-design-uni/components/wd-img/index'))['default']
    'wd-img-cropper': (typeof import('wot-design-uni/components/wd-img-cropper/index'))['default']
    'wd-progress': (typeof import('wot-design-uni/components/wd-progress/index'))['default']
    'wd-skeleton': (typeof import('wot-design-uni/components/wd-skeleton/index'))['default']
    'wd-status-tip': (typeof import('wot-design-uni/components/wd-status-tip/index'))['default']
    'wd-steps': (typeof import('wot-design-uni/components/wd-steps/index'))['default']
    'wd-steps-item': (typeof import('wot-design-uni/components/wd-steps-item/index'))['default']
    'wd-swiper': (typeof import('wot-design-uni/components/wd-swiper/index'))['default']
    'wd-swiper-item': (typeof import('wot-design-uni/components/wd-swiper-item/index'))['default']
    'wd-tag': (typeof import('wot-design-uni/components/wd-tag/index'))['default']

    // 导航组件
    'wd-grid': (typeof import('wot-design-uni/components/wd-grid/index'))['default']
    'wd-grid-item': (typeof import('wot-design-uni/components/wd-grid-item/index'))['default']
    'wd-navbar': (typeof import('wot-design-uni/components/wd-navbar/index'))['default']
    'wd-tab-bar': (typeof import('wot-design-uni/components/wd-tab-bar/index'))['default']
    'wd-tabs': (typeof import('wot-design-uni/components/wd-tabs/index'))['default']
    'wd-tabbar': (typeof import('wot-design-uni/components/wd-tabbar/index'))['default']
    'wd-tabbar-item': (typeof import('wot-design-uni/components/wd-tabbar-item/index'))['default']
  }
}

// 扩展WdUI的全局类型
declare global {
  interface Uni {
    $wdui: {
      toast: (typeof import('wot-design-uni/components/wd-toast/index'))['default']
      messageBox: (typeof import('wot-design-uni/components/wd-message-box/index'))['default']
      notify: (typeof import('wot-design-uni/components/wd-notify/index'))['default']
    }
  }
}
