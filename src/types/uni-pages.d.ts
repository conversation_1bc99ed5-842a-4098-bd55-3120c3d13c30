/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/tab/home/<USER>" |
       "/pages/loan/auth" |
       "/pages/loan/contact" |
       "/pages/loan/face" |
       "/pages/loan/info" |
       "/pages/loan/result" |
       "/pages/shop/activity" |
       "/pages/shop/category" |
       "/pages/shop/order-confirm" |
       "/pages/shop/order-detail" |
       "/pages/shop/order-list" |
       "/pages/shop/payment-result" |
       "/pages/shop/payment" |
       "/pages/shop/shop-detail" |
       "/pages/shop/shop-list" |
       "/pages/user/address-edit" |
       "/pages/user/address-list" |
       "/pages/user/bank-card-add" |
       "/pages/user/bank-card" |
       "/pages/user/settings" |
       "/pages/common/404/index" |
       "/pages/common/login/index" |
       "/pages/common/webview/index" |
       "/pages/tab/shop/index" |
       "/pages/tab/user/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/tab/home/<USER>" | "/pages/tab/shop/index" | "/pages/tab/user/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
