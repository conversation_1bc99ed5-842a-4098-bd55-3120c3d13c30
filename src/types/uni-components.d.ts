/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// 为uni官方组件库添加TypeScript提示

import '@uni-helper/uni-app-types'

declare module 'vue' {
  export interface GlobalComponents {
    // 基础组件
    button: (typeof import('@uni-helper/uni-app-types/components'))['Button']
    checkbox: (typeof import('@uni-helper/uni-app-types/components'))['Checkbox']
    'checkbox-group': (typeof import('@uni-helper/uni-app-types/components'))['CheckboxGroup']
    form: (typeof import('@uni-helper/uni-app-types/components'))['Form']
    icon: (typeof import('@uni-helper/uni-app-types/components'))['Icon']
    image: (typeof import('@uni-helper/uni-app-types/components'))['Image']
    input: (typeof import('@uni-helper/uni-app-types/components'))['Input']
    label: (typeof import('@uni-helper/uni-app-types/components'))['Label']
    picker: (typeof import('@uni-helper/uni-app-types/components'))['Picker']
    'picker-view': (typeof import('@uni-helper/uni-app-types/components'))['PickerView']
    'picker-view-column': (typeof import('@uni-helper/uni-app-types/components'))['PickerViewColumn']
    radio: (typeof import('@uni-helper/uni-app-types/components'))['Radio']
    'radio-group': (typeof import('@uni-helper/uni-app-types/components'))['RadioGroup']
    'rich-text': (typeof import('@uni-helper/uni-app-types/components'))['RichText']
    'scroll-view': (typeof import('@uni-helper/uni-app-types/components'))['ScrollView']
    slider: (typeof import('@uni-helper/uni-app-types/components'))['Slider']
    swiper: (typeof import('@uni-helper/uni-app-types/components'))['Swiper']
    'swiper-item': (typeof import('@uni-helper/uni-app-types/components'))['SwiperItem']
    switch: (typeof import('@uni-helper/uni-app-types/components'))['Switch']
    text: (typeof import('@uni-helper/uni-app-types/components'))['Text']
    textarea: (typeof import('@uni-helper/uni-app-types/components'))['Textarea']
    view: (typeof import('@uni-helper/uni-app-types/components'))['View']
    'web-view': (typeof import('@uni-helper/uni-app-types/components'))['WebView']
    'match-media': (typeof import('@uni-helper/uni-app-types/components'))['MatchMedia']
    'movable-area': (typeof import('@uni-helper/uni-app-types/components'))['MovableArea']
    'movable-view': (typeof import('@uni-helper/uni-app-types/components'))['MovableView']
    'cover-image': (typeof import('@uni-helper/uni-app-types/components'))['CoverImage']
    'cover-view': (typeof import('@uni-helper/uni-app-types/components'))['CoverView']
    progress: (typeof import('@uni-helper/uni-app-types/components'))['Progress']

    // 媒体组件
    audio: (typeof import('@uni-helper/uni-app-types/components'))['Audio']
    camera: (typeof import('@uni-helper/uni-app-types/components'))['Camera']
    'live-player': (typeof import('@uni-helper/uni-app-types/components'))['LivePlayer']
    'live-pusher': (typeof import('@uni-helper/uni-app-types/components'))['LivePusher']
    video: (typeof import('@uni-helper/uni-app-types/components'))['Video']

    // 地图组件
    map: (typeof import('@uni-helper/uni-app-types/components'))['Map']

    // 画布
    canvas: (typeof import('@uni-helper/uni-app-types/components'))['Canvas']

    // 页面属性配置组件
    'navigation-bar': (typeof import('@uni-helper/uni-app-types/components'))['NavigationBar']
    'page-meta': (typeof import('@uni-helper/uni-app-types/components'))['PageMeta']

    // 开放能力
    ad: (typeof import('@uni-helper/uni-app-types/components'))['Ad']
    'ad-content-page': (typeof import('@uni-helper/uni-app-types/components'))['AdContentPage']
    'official-account': (typeof import('@uni-helper/uni-app-types/components'))['OfficialAccount']

    // 导航
    navigator: (typeof import('@uni-helper/uni-app-types/components'))['Navigator']
  }
}
