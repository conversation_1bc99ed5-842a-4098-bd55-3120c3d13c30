import { ComponentCustomProperties } from 'vue'
import { ToastType, ToastOptionsWithType } from '@/utils/global'
import Utils from '@/utils/global'

// 导入自定义类型定义文件
import './uni-components.d'
import './wot-design-uni.d'
import './z-paging.d'
import './lime-echart.d'
import './uni-app-extend.d'

declare module 'vue' {
  interface ComponentCustomProperties {
    $assets: any
  }
}

// 扩展全局声明
declare global {
  // z-paging组件声明
  declare type zPaging = any

  // 增强uni类型
  interface Uni {
    $u: any
  }

  // Utils类型声明
  declare const Utils: typeof Utils

  // Toast类型声明
  declare namespace Utils {
    function toast(options: ToastOptionsWithType | string): void
  }

  // vue node声明
  declare type VueNode = VNodeChild | JSX.Element

  declare type TimeoutHandle = ReturnType<typeof setTimeout>
  declare type IntervalHandle = ReturnType<typeof setInterval>
}
