/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Vue显式导入类型支持

declare module 'vue' {
  // 核心响应式API
  export { 
    ref, shallowRef, isRef, toRef, toRefs, unref, customRef, triggerRef,
    reactive, readonly, isReactive, isReadonly, shallowReactive, shallowReadonly, toRaw, markRaw, isProxy,
    computed, watch, watchEffect, watchPostEffect, watchSyncEffect, effectScope, getCurrentScope, onScopeDispose
  } from '@vue/runtime-core'

  // 组件API
  export { 
    defineComponent, defineAsyncComponent, 
    getCurrentInstance, h, 
    provide, inject, 
    nextTick,
    useCssModule, 
    createApp
  } from '@vue/runtime-core'

  // 生命周期钩子
  export { 
    onBeforeMount, onMounted, 
    onBeforeUpdate, onUpdated, 
    onBeforeUnmount, onUnmounted, 
    onActivated, onDeactivated, 
    onErrorCaptured, onRenderTracked, onRenderTriggered,
    onServerPrefetch
  } from '@vue/runtime-core'

  // 类型定义
  export { PropType, ComponentPublicInstance, ExtractPropTypes, ExtractDefaultPropTypes } from '@vue/runtime-core'
  export type { 
    Ref, ToRef, ToRefs, UnwrapRef, 
    ComputedRef, WritableComputedRef, 
    WatchStopHandle, WatchCallback, WatchSource, WatchEffect,
    ComponentInternalInstance, ComponentCustomProperties,
    VNode, Component, DefineComponent
  } from '@vue/runtime-core'

  // DOM相关类型
  export type { CSSProperties } from '@vue/runtime-dom'

  // Volar支持
  export { useAttrs, useSlots, useModel } from '@vue/runtime-core'
}
