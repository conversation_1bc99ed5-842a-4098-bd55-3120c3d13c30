/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// z-paging组件库类型声明

declare module 'vue' {
  export interface GlobalComponents {
    'z-paging': {
      /**
       * 组件数据请求方法，使用page构建模式需要传入此方法
       */
      reload: () => void
      /**
       * 【通过ref调用】刷新列表数据，pageNo恢复为默认值，相当于下拉刷新的效果
       */
      refresh: () => void
      /**
       * 【通过ref调用】清空分页数据
       */
      clear: () => void
      /**
       * 【通过ref调用】当分页是通过服务端处理时，设置当前的分页数据
       * @param data 分页数据数组
       * @param success 请求是否成功，如果传递false，将显示错误提示
       * @param totalData 服务端返回的总数据（用于分页计算等）
       * @param isLocal 是否为本地分页，默认为false
       */
      complete: (data: any[], success?: boolean, totalData?: any, isLocal?: boolean) => void
      /**
       * 【通过ref调用】手动触发滚动到底部刷新
       * @param animate 是否使用动画滚动，默认为true
       */
      doLoadMore: (animate?: boolean) => void
      /**
       * 【通过ref调用】手动设置为空数据状态
       * @param emptyViewText 自定义空数据状态提示文字
       */
      showEmptyView: (emptyViewText?: string) => void
      /**
       * 【通过ref调用】清除所有内容，包括分页数据、请求状态等
       */
      resetTotalData: () => void
      /**
       * 【通过ref调用】自动设置滚动条的位置
       * @param scrollTop 滚动的位置，单位为px
       */
      scrollToTop: (scrollTop: number) => void
      /**
       * 【通过ref调用】手动触发下拉刷新
       * @param animate 是否使用动画，默认为true
       */
      doPullRefresh: (animate?: boolean) => void
      /**
       * 【通过ref调用】手动更新列表高度
       */
      updatePageScrollHeight: () => void
      /**
       * 【通过ref调用】手动更新缓存的列表高度
       */
      updateCacheHeight: () => void
      /**
       * 【通过ref调用】获取当前z-paging的总高度
       * @return {number} 高度值，单位为px
       */
      getHeight: () => number
      /**
       * 当前的分页页码
       */
      pageNo: number
      /**
       * 当前的分页大小
       */
      pageSize: number
      /**
       * 当前的总数据量
       */
      totalData: number
    }
  }
}

// 为全局声明zPaging添加类型
declare global {
  const zPaging: {
    /**
     * 设置全局默认配置
     * @param config 全局配置
     */
    setConfig: (config: Record<string, any>) => void
    /**
     * 获取当前全局配置
     */
    getConfig: () => Record<string, any>
  }
}
