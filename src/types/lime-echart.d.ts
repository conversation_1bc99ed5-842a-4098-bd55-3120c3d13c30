/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// lime-echart组件库类型声明

declare module 'vue' {
  export interface GlobalComponents {
    'l-echart': {
      /**
       * 图表实例，可调用echarts的所有API
       */
      chart: any
      /**
       * 图表选项，符合ECharts配置项
       */
      option: Record<string, any>
      /**
       * 图表渲染器类型，支持canvas和svg
       */
      renderer: 'canvas' | 'svg'
      /**
       * 图表主题
       */
      theme: string
      /**
       * 图表宽度，默认为100%
       */
      width: string | number
      /**
       * 图表高度，默认为100%
       */
      height: string | number
      /**
       * 是否禁用图表
       */
      disabled: boolean
      /**
       * 是否显示加载状态
       */
      loading: boolean
      /**
       * 加载文本
       */
      loadingText: string
      /**
       * 是否跟随容器大小自动变化
       */
      resizable: boolean
      /**
       * 延迟渲染时间，单位为ms
       */
      canvasToTempFilePath: () => Promise<string>
      /**
       * 设置图表配置项
       * @param option ECharts配置项
       * @param notMerge 是否不合并老的配置，默认false
       * @param lazyUpdate 是否懒更新，默认false
       */
      setOption: (option: Record<string, any>, notMerge?: boolean, lazyUpdate?: boolean) => void
      /**
       * 更新图表尺寸
       */
      resize: () => void
      /**
       * 显示加载动画
       * @param options 加载配置项
       */
      showLoading: (options?: Record<string, any>) => void
      /**
       * 隐藏加载动画
       */
      hideLoading: () => void
      /**
       * 清空图表
       */
      clear: () => void
      /**
       * 销毁图表实例
       */
      dispose: () => void
    }
    'lime-echart': {
      /**
       * 图表实例，可调用echarts的所有API
       */
      chart: any
      /**
       * 图表选项，符合ECharts配置项
       */
      option: Record<string, any>
      /**
       * 图表渲染器类型，支持canvas和svg
       */
      renderer: 'canvas' | 'svg'
      /**
       * 图表主题
       */
      theme: string
      /**
       * 图表宽度，默认为100%
       */
      width: string | number
      /**
       * 图表高度，默认为100%
       */
      height: string | number
      /**
       * 是否禁用图表
       */
      disabled: boolean
      /**
       * 是否显示加载状态
       */
      loading: boolean
      /**
       * 加载文本
       */
      loadingText: string
      /**
       * 是否跟随容器大小自动变化
       */
      resizable: boolean
      /**
       * 延迟渲染时间，单位为ms
       */
      canvasToTempFilePath: () => Promise<string>
      /**
       * 设置图表配置项
       * @param option ECharts配置项
       * @param notMerge 是否不合并老的配置，默认false
       * @param lazyUpdate 是否懒更新，默认false
       */
      setOption: (option: Record<string, any>, notMerge?: boolean, lazyUpdate?: boolean) => void
      /**
       * 更新图表尺寸
       */
      resize: () => void
      /**
       * 显示加载动画
       * @param options 加载配置项
       */
      showLoading: (options?: Record<string, any>) => void
      /**
       * 隐藏加载动画
       */
      hideLoading: () => void
      /**
       * 清空图表
       */
      clear: () => void
      /**
       * 销毁图表实例
       */
      dispose: () => void
    }
  }
}
