import { ref, onMounted, computed } from 'vue'
import { pages } from '@/pages.json'

export function usePageConfig() {
  const pageConfigRef = ref(null)

  // 使用计算属性提供直接访问的方式
  const config = computed(() => pageConfigRef.value)

  onMounted(() => {
    try {
      const currentPages = getCurrentPages()
      if (currentPages.length > 0) {
        const currentPage = currentPages[currentPages.length - 1]
        const pageInstance = currentPage as any
        const route = pageInstance.route

        const pageConfigInfo = pages.find(p => p.path === route)
        pageConfigRef.value = pageConfigInfo || null
      }
    } catch (error) {
      console.error('获取页面配置失败:', error)
    }
  })

  // 返回多种访问方式
  return {
    pageConfig: pageConfigRef // 原始 RefImpl 对象
  }
}
