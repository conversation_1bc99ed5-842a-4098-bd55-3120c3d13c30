import { defineStore } from 'pinia'
import type { ToastOptions } from 'wot-design-uni/components/wd-toast/types'
import Utils from '@/utils/global'

// 获取当前页面路径
function getCurrentPath(): string {
  const pages = getCurrentPages()
  return pages.length > 0 ? pages[pages.length - 1].route || '' : ''
}

interface GlobalToast {
  toastOptions: ToastOptions
  currentPage: string
}

const defaultOptions: ToastOptions = {
  duration: 2000,
  show: false
}

export const useGlobalToast = defineStore('global-toast', {
  state: (): GlobalToast => ({
    toastOptions: defaultOptions,
    currentPage: ''
  }),
  getters: {},
  actions: {
    // 打开Toast
    show(option: ToastOptions | string) {
      this.currentPage = getCurrentPath()
      const options = Utils.deepMerge(
        defaultOptions,
        typeof option === 'string' ? { msg: option } : option
      ) as ToastOptions
      this.toastOptions = Utils.deepMerge(options, {
        show: true,
        position: options.position || 'middle-top'
      }) as ToastOptions
    },
    // 成功提示
    success(option: ToastOptions | string) {
      this.show(
        Utils.deepMerge(
          {
            iconName: 'success',
            duration: 2000
          },
          typeof option === 'string' ? { msg: option } : option
        ) as ToastOptions
      )
    },
    // 错误提示
    error(option: ToastOptions | string) {
      this.show(
        Utils.deepMerge(
          {
            iconName: 'error',
            direction: 'vertical'
          },
          typeof option === 'string' ? { msg: option } : option
        ) as ToastOptions
      )
    },
    // 常规提示
    info(option: ToastOptions | string) {
      this.show(
        Utils.deepMerge(
          {
            iconName: 'info'
          },
          typeof option === 'string' ? { msg: option } : option
        ) as ToastOptions
      )
    },
    // 警告提示
    warning(option: ToastOptions | string) {
      this.show(
        Utils.deepMerge(
          {
            iconName: 'warning'
          },
          typeof option === 'string' ? { msg: option } : option
        ) as ToastOptions
      )
    },
    // 关闭Toast
    close() {
      this.toastOptions = defaultOptions
      this.currentPage = ''
    }
  }
})
