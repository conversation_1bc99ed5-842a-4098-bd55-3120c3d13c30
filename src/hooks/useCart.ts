import { ref } from 'vue'
import {
  addToCartApi,
  getCartList<PERSON>pi,
  updateCartQuantityApi,
  removeFromCartApi,
  clearCartApi,
  getCartQuantityApi
} from '@/api/shop'

/**
 * 购物车相关功能Hook
 * 提供加入购物车、获取购物车列表、更新购物车商品数量、移除购物车商品、清空购物车等功能
 */
export function useCart() {
  // 加载状态
  const loading = ref(false)
  // 错误信息
  const error = ref<string | null>(null)
  // 购物车商品数量
  const cartCount = ref(0)
  // 购物车列表
  const cartList = ref<API.Cart.CartItem[]>([])

  /**
   * 添加商品到购物车
   * @param productId 商品ID
   * @param quantity 商品数量，默认为1
   * @param showTip 是否显示提示，默认为true
   * @returns Promise<boolean> 是否添加成功
   */
  const addToCart = async (data: { product_id: string | number; quantity: number; sku_id: number }, showTip = true) => {
    loading.value = true
    error.value = null
    try {
      console.log(`data -->`, data)
      const [res, err] = await addToCartApi(data)

      showTip &&
        Utils.toast({
          type: 'success',
          msg: '加入购物车成功'
        })
      return true
    } catch (e: any) {
      error.value = e.message || '添加失败'
      showTip && Utils.toast(error.value)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取购物车商品数量
   */
  const getCartCount = async () => {
    try {
      const [res, err] = await getCartQuantityApi()
      if (err) {
        return
      }
      cartCount.value = res.data?.count || 0
    } catch (e) {
      console.error('获取购物车数量失败', e)
    }
  }

  /**
   * 获取购物车列表
   */
  const fetchCartList = async () => {
    loading.value = true
    error.value = null
    try {
      const [res, err] = await getCartListApi()
      if (err) {
        error.value = err.message || '获取购物车失败'
        return []
      }
      cartList.value = res.data?.items || []
      return cartList.value
    } catch (e: any) {
      error.value = e.message || '获取购物车失败'
      return []
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新购物车商品数量
   * @param cartId 购物车ID
   * @param quantity 更新后的数量
   */
  const updateCartQuantity = async (cartId: string | number, quantity: number) => {
    loading.value = true
    error.value = null
    try {
      const [res, err] = await updateCartQuantityApi({ cart_id: cartId, quantity })
      if (err) {
        error.value = err.message || '更新失败'
        Utils.toast(error.value)
        return false
      }
      return true
    } catch (e: any) {
      console.log(`1 -->`, 1)
      error.value = e.message || '更新失败'
      Utils.toast(error.value)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 从购物车中移除商品
   * @param cartIds 要移除的购物车商品ID数组
   */
  const removeFromCart = async (cartIds: Array<string | number>) => {
    console.log(`cartIds -->`, cartIds)
    loading.value = true
    error.value = null
    try {
      const [res, err] = await removeFromCartApi({ cart_ids: cartIds })
      if (err) {
        error.value = err.message || '移除失败'
        Utils.toast(error.value)
        return false
      }
      Utils.toast('移除成功')
      // 更新购物车数据
      await fetchCartList()
      return true
    } catch (e: any) {
      error.value = e.message || '移除失败'
      Utils.toast(error.value)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 清空购物车
   */
  const clearCart = async () => {
    loading.value = true
    error.value = null
    try {
      const [res, err] = await clearCartApi()
      if (err) {
        error.value = err.message || '清空购物车失败'
        Utils.toast(error.value)
        return false
      }
      cartList.value = []
      cartCount.value = 0
      Utils.toast('购物车已清空')
      return true
    } catch (e: any) {
      error.value = e.message || '清空购物车失败'
      Utils.toast(error.value)
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    cartCount,
    cartList,
    addToCart,
    getCartCount,
    fetchCartList,
    updateCartQuantity,
    removeFromCart,
    clearCart
  }
}
