import { defineStore } from 'pinia'
import type { ToastOptions } from 'wot-design-uni/components/wd-toast/types'
import Utils from '@/utils/global'

// 获取当前页面路径
function getCurrentPath(): string {
  const pages = getCurrentPages()
  return pages.length > 0 ? pages[pages.length - 1].route || '' : ''
}

interface GlobalLoading {
  loadingOptions: ToastOptions
  currentPage: string
}

const defaultOptions: ToastOptions = {
  show: false
}

export const useGlobalLoading = defineStore('global-loading', {
  state: (): GlobalLoading => ({
    loadingOptions: defaultOptions,
    currentPage: ''
  }),
  getters: {},
  actions: {
    // 加载提示
    loading(option: ToastOptions | string) {
      this.currentPage = getCurrentPath()
      this.loadingOptions = Utils.deepMerge(
        {
          iconName: 'loading',
          duration: 0,
          cover: true,
          position: 'middle',
          show: true
        },
        typeof option === 'string' ? { msg: option } : option
      ) as ToastOptions
    },
    // 关闭Loading
    close() {
      this.loadingOptions = defaultOptions
      this.currentPage = ''
    }
  }
})
