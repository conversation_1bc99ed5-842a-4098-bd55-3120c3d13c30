import { defineStore } from 'pinia'
import type { MessageOptions, MessageResult } from 'wot-design-uni/components/wd-message-box/types'
import Utils from '@/utils/global'

// 获取当前页面路径
function getCurrentPath(): string {
  const pages = getCurrentPages()
  return pages.length > 0 ? pages[pages.length - 1].route || '' : ''
}

export type GlobalMessageOptions = MessageOptions & {
  success?: (res: MessageResult) => void
  fail?: (res: MessageResult) => void
}

interface GlobalMessage {
  messageOptions: GlobalMessageOptions | null
  currentPage: string
}

export const useGlobalMessage = defineStore('global-message', {
  state: (): GlobalMessage => ({
    messageOptions: null,
    currentPage: ''
  }),
  actions: {
    show(option: GlobalMessageOptions | string) {
      this.currentPage = getCurrentPath()
      this.messageOptions = {
        ...(Utils.isString(option) ? { title: option } : option),
        cancelButtonProps: {
          round: false
        },
        confirmButtonProps: {
          round: false
        }
      }
    },
    alert(option: GlobalMessageOptions | string) {
      const messageOptions = Utils.deepMerge(
        { type: 'alert' },
        Utils.isString(option) ? { title: option } : option
      ) as MessageOptions
      messageOptions.showCancelButton = false
      this.show(messageOptions)
    },
    confirm(option: GlobalMessageOptions | string) {
      const messageOptions = Utils.deepMerge(
        { type: 'confirm' },
        Utils.isString(option) ? { title: option } : option
      ) as MessageOptions
      messageOptions.showCancelButton = true
      this.show(messageOptions)
    },
    prompt(option: GlobalMessageOptions | string) {
      const messageOptions = Utils.deepMerge(
        { type: 'prompt' },
        Utils.isString(option) ? { title: option } : option
      ) as MessageOptions
      messageOptions.showCancelButton = true
      this.show(messageOptions)
    },
    close() {
      this.messageOptions = null
      this.currentPage = ''
    }
  }
})
