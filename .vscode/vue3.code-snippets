{
  "Uni-Plus Vue3 SFC": {
    "scope": "vue",
    "prefix": "v3",
    "description": "vue3 的 uni-plus sfc 文件模板",
    "body": [
      "<!--",
      "@description: $1",
      "@creationTime: ${CURRENT_YEAR}-${CURRENT_MONTH}-${CURRENT_DATE}",
      "-->\n",
      "<route type=\"page\" lang=\"json5\" >",
      "{",
      "  layout: 'default',",
      "  style: {",
      "    navigationBarTitleText: '$2',",
      "  },",
      "}",
      "</route>\n",
      "<template>",
      "  <view>$3</view>",
      "</template>\n",
      "<script lang=\"ts\" setup>",
      "/* ------------------------ 导入 与 引用 ----------------------------------- */",
      "/* ------------------------ 函数 与 方法 ----------------------------------- */",
      "/* ------------------------- 生命周期 -------------------------------------- */",
      "</script>\n",
      "<style lang=\"scss\" scoped></style>\n",
    ],
  }
}
