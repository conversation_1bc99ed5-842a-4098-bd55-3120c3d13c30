# Changelog

## 1.0.0 (2025-02-11)

- ✨ feat: 适配 H5 与 微信小程序端 ([c438d98](https://github.com/DaMaiCoding/uni-plus/commit/c438d98))
- ✨ feat: 完善 首页基本功能 ([27773cd](https://github.com/DaMaiCoding/uni-plus/commit/27773cd))
- ✨ feat: 优化 暗黑模式 与 增加各个案例的链接 ([2a9a70b](https://github.com/DaMaiCoding/uni-plus/commit/2a9a70b))
- ✨ feat: 增加 demo 导航页 ([e107631](https://github.com/DaMaiCoding/uni-plus/commit/e107631))
- ✨ feat: 增加 README.md 与 LICENSE 开源协议 ([582cd6d](https://github.com/DaMaiCoding/uni-plus/commit/582cd6d))
- 🎈 perf: 去掉 vue-i18n，使用自定义代替 ([6833bd9](https://github.com/DaMaiCoding/uni-plus/commit/6833bd9))
- 🎉 init: 项目初始化 ([baf10db](https://github.com/DaMaiCoding/uni-plus/commit/baf10db))
- 🐎 ci: 修复 CI build 报错 ([b5fed26](https://github.com/DaMaiCoding/uni-plus/commit/b5fed26))
- 🐎 ci: 修复 CI build 报错 ([23c00d0](https://github.com/DaMaiCoding/uni-plus/commit/23c00d0))
- 🐎 ci: 增加 在线 DEMO ([65c571b](https://github.com/DaMaiCoding/uni-plus/commit/65c571b))
- 🐞 fix: 修复 全局 CSS 变量不生效问题 ([0d35ac9](https://github.com/DaMaiCoding/uni-plus/commit/0d35ac9))
- 🐞 fix: 修复 uni-plus DEMO 不显示问题 ([d4f3a86](https://github.com/DaMaiCoding/uni-plus/commit/d4f3a86))
- 📃 docs: 优化 README.md ([f1a1b59](https://github.com/DaMaiCoding/uni-plus/commit/f1a1b59))
- 📃 docs: 增加 README 兼容性 环境配置 ([22787a1](https://github.com/DaMaiCoding/uni-plus/commit/22787a1))

## <small>0.0.12 (2025-01-02)</small>

- ✨ feat: git 提交规范化 ([cc31756](https://gitee.com/FOM/uni-plus/commits/cc31756))
- 🎈 perf: 去掉没必要的 eslint 缓存 ([d70797e](https://gitee.com/FOM/uni-plus/commits/d70797e))
- 🎈 perf: 优化 git 提交 ([208478d](https://gitee.com/FOM/uni-plus/commits/208478d))

## <small>0.0.1 (2024-12-31)</small>

- ✨ feat: 新增 emoji ([7865661](https://gitee.com/FOM/uni-plus/commits/7865661))
- uni-plus 项目初始化 ([be9e6fa](https://gitee.com/FOM/uni-plus/commits/be9e6fa))
