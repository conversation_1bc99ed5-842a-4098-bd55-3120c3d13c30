import {
  defineConfig,
  presetUno,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup
} from 'unocss'

import { presetApplet, presetRemRpx, transformerApplet, transformerAttributify } from 'unocss-applet'
import { presetUni } from '@uni-helper/unocss-preset-uni'

// 判断是否是小程序
const isApplet = process.env?.UNI_PLATFORM?.startsWith('mp-') ?? false

// 自定义规则
const customRules = [
  // 组合用法 (例如 border-b-1rpx-#eee)
  [
    /^border-b-(\d+)rpx-#([0-9a-fA-F]{3,8})$/,
    ([, d, c]) => ({
      'border-bottom-width': `${d}rpx`,
      'border-bottom-style': 'solid',
      'border-bottom-color': `#${c}`
    })
  ],

  // 安全区域配置
  [
    'p-safe',
    {
      padding:
        'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)'
    }
  ],
  ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
  ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
  [
    'ptb-safe',
    {
      'padding-top': 'env(safe-area-inset-top)',
      'padding-bottom': 'env(safe-area-inset-bottom)'
    }
  ],

  // 主题色相关渐变
  ['bg-gradient-primary', { 'background-image': 'linear-gradient(94.85deg, #FE5856 0%, #FE3635 100%)' }],
  ['bg-gradient-primary-disabled', { 'background-image': 'linear-gradient(94.85deg, #FA8E8C 0%, #FA7877 100%)' }]
]

// 自定义快捷方式
const customShortcuts = [
  { 'flex-center': 'flex justify-center items-center' },
  { 'flex-col-center': 'flex justify-center items-center flex-col' },
  { 'bg-img-contain': 'bg-no-repeat bg-center bg-contain' },
  { 'bg-img-cover': 'bg-no-repeat bg-center bg-cover' },
  { 'btn-primary': 'w-full h-80rpx flex-center text-white rounded-40rpx !bg-gradient-primary text-28rpx' },
  {
    'btn-primary-disabled':
      'w-full h-80rpx flex-center !text-white text-28rpx rounded-40rpx !bg-gradient-primary-disabled'
  }
]

export default defineConfig({
  presets: [
    // 添加uni-app专用预设
    presetUni() as any,
    // 根据平台选择不同的预设
    ...(isApplet
      ? [presetApplet(), presetRemRpx()]
      : [
          presetUno(),
          presetAttributify({
            prefixedOnly: true, // 只支持以 `ul-` 开头的类名
            prefix: 'ul'
          })
        ]),
    // 支持图标，需要搭配图标库，eg: @iconify-json/uiw, 使用 <i class="i-uiw-alipay" />
    presetIcons({
      scale: 1.2, // 图标大小
      warn: true, // 警告
      prefix: ['i-'], // 图标前缀
      // 额外的 CSS 属性来控制图标的默认行为，默认内联图标
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle'
      }
    })
  ] as any,
  // transformers 是转换器
  transformers: isApplet
    ? [
        transformerApplet(),
        transformerAttributify({
          prefixedOnly: true,
          prefix: 'ul'
        })
      ]
    : [transformerDirectives(), transformerVariantGroup()],
  // shortcuts 自定义样式快捷方式
  shortcuts: customShortcuts as any,
  // rules 的作用是：在全局范围内添加自定义规则
  rules: customRules as any,
  // 自定义平台匹配规则
  theme: {
    colors: {
      primary: 'var(--primary-color, #ff3c29)',
      secondary: 'var(--secondary-color, #0088ff)',
      success: 'var(--success-color, #00cc66)',
      warning: 'var(--warning-color, #ffcc00)',
      error: 'var(--error-color, #ff4d4f)',

      // 也可以使用对象定义不同深浅的颜色
      gray: {
        light: 'var(--gray-light, #f5f5f5)',
        DEFAULT: 'var(--gray, #888888)',
        dark: 'var(--gray-dark, #333333)'
      }
    },
    platforms: {
      // 这里可以添加自定义平台
    }
  }
})

/**
 * 最终这一套组合下来会得到：
 * mp 里面：mt-4 => margin-top: 32rpx  == 16px
 * h5 里面：mt-4 => margin-top: 1rem == 16px
 *
 * 如果是传统方式写样式，则推荐设计稿设置为 750，这样设计稿1px，代码写1rpx。
 * rpx是响应式的，可以让不同设备的屏幕显示效果保持一致。
 */
