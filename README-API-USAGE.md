# API 请求封装使用指南

这个文档介绍了项目中 HTTP 请求的统一封装和使用方法。

## 核心功能

- **统一的返回格式**：所有 API 都返回相同的结构（code、msg/message、data）
- **双重调用方式**：支持两种风格的调用
- **类型安全**：完全支持 TypeScript 类型推断

## 使用方法

### 1. 定义 API

```typescript
// src/api/someApi.ts
import http from '@/http/requestWrapper'

export const getUserApi = (userId?: string) => {
  return http.get('/user/info', { query: { userId } })
}

export const updateUserApi = (data?: any) => {
  return http.post('/user/update', { data })
}
```

### 2. 调用 API - await-to-js 风格

```typescript
import { getUserApi } from '@/api/someApi'

async function fetchUser() {
  const [res, err] = await getUserApi('123')

  if (err) {
    // 处理错误
    console.error('获取用户失败:', err)
    return
  }

  // 使用响应数据
  console.log('用户信息:', res.code, res.msg, res.data)
}
```

### 3. 调用 API - 直接 await 风格

```typescript
import { updateUserApi } from '@/api/someApi'

async function updateUser() {
  try {
    const res = await updateUserApi({ name: '张三' }).unwrap()

    // 使用响应数据
    console.log('更新结果:', res.code, res.msg, res.data)
  } catch (err) {
    // 处理错误
    console.error('更新失败:', err)
  }
}
```

### 4. 直接使用 http 工具

```typescript
import http from '@/http/requestWrapper'

async function directApiCall() {
  // await-to-js 风格
  const [res1, err1] = await http.get('/some/api', { query: { id: 1 } })

  // 直接 await 风格
  try {
    const res2 = await http.post('/another/api', { data: { name: '李四' } }).unwrap()
  } catch (err) {
    // 处理错误
  }
}
```

## 类型定义

```typescript
// API 响应类型
interface ApiResponse<T = any> {
  code: number
  msg?: string
  message?: string
  data: T
}

// 请求选项
interface RequestPayload {
  data?: any
  query?: Record<string, any>
  hideErrorToast?: boolean
  // ...其他选项
}
```

## 最佳实践

1. **总是在 API 层定义函数**，而不是直接在业务代码中使用 http
2. **根据业务场景选择调用风格**：
   - 如果需要精细控制错误处理，使用 await-to-js 风格
   - 如果使用 try-catch 更自然，使用 unwrap() 风格
3. **为 API 添加类型**：
   ```typescript
   export const getUserApi = (userId?: string) => {
     return http.get<UserInfo>('/user/info', { query: { userId } })
   }
   ```

## 注意事项

- `unwrap()` 方法在请求失败时会抛出异常，必须使用 try-catch 捕获
- 所有 API 都会返回完整的响应对象，包含 code、msg/message、data
