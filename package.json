{"name": "uni-plus", "version": "1.0.0", "type": "commonjs", "author": {"name": "DaMaiCoding", "zhName": "大麦大麦", "email": "<EMAIL>", "github": "https://github.com/DaMaiCoding", "gitee": "https://gitee.com/DaMaiCoding"}, "license": "MIT", "repository": "https://github.com/DaMaiCoding/uni-plus", "repository-gitee": "https://gitee.com/DaMaiCoding/uni-plus", "bugs": {"url": "https://github.com/DaMaiCoding/uni-plus/issues"}, "homepage": "https://damaicoding.github.io/uni-plus/", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "prepare": "husky", "release": "release-it", "type-check": "vue-tsc --noEmit", "cz": "czg emoji", "lint:fix": "eslint --fix \"src/**/*.{vue,js,ts}\"", "lint:unocss": "eslint --fix \"src/**/*.vue\" --rule \"unocss/order: error\" --rule \"unocss/order-attributify: error\"", "sort:vue": "eslint --fix \"src/pages/**/*.vue\"", "clean": "rimraf node_modules", "clean:cache": "rimraf node_modules/.cache"}, "lint-staged": {"**/*.{vue,js,ts,jsx,tsx,mjs,cjs,html,json,md}": ["prettier --write --loglevel=error"], "**/*.{vue,js,ts,jsx,tsx,mjs,cjs}": ["eslint --fix --quiet --max-warnings=999"], "**/*.vue": ["eslint --fix --quiet --rule \"unocss/order: error\" --rule \"unocss/order-attributify: error\"", "pnpm run sort:vue"], "**/*.{vue,css,scss,html}": ["stylelint --fix --quiet --max-warnings=999"]}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4040520250104002", "@dcloudio/uni-app-harmony": "3.0.0-4040520250104002", "@dcloudio/uni-app-plus": "3.0.0-4040520250104002", "@dcloudio/uni-components": "3.0.0-4040520250104002", "@dcloudio/uni-h5": "3.0.0-4040520250104002", "@dcloudio/uni-mp-alipay": "3.0.0-4040520250104002", "@dcloudio/uni-mp-baidu": "3.0.0-4040520250104002", "@dcloudio/uni-mp-jd": "3.0.0-4040520250104002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4040520250104002", "@dcloudio/uni-mp-lark": "3.0.0-4040520250104002", "@dcloudio/uni-mp-qq": "3.0.0-4040520250104002", "@dcloudio/uni-mp-toutiao": "3.0.0-4040520250104002", "@dcloudio/uni-mp-weixin": "3.0.0-4040520250104002", "@dcloudio/uni-mp-xhs": "3.0.0-4040520250104002", "@dcloudio/uni-quickapp-webview": "3.0.0-4040520250104002", "czg": "^1.11.0", "echarts": "^5.6.0", "husky": "^9.1.7", "lodash-es": "^4.17.21", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^3.2.3", "vue": "^3.4.21", "wot-design-uni": "^1.10.0", "z-paging": "^2.8.4"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4040520250104002", "@dcloudio/uni-cli-shared": "3.0.0-4040520250104002", "@dcloudio/uni-stacktracey": "3.0.0-4040520250104002", "@dcloudio/vite-plugin-uni": "3.0.0-4040520250104002", "@eslint/js": "^9.17.0", "@iconify-json/uiw": "^1.2.1", "@release-it/conventional-changelog": "^9.0.4", "@types/wechat-miniprogram": "^3.4.8", "@typescript-eslint/parser": "^8.18.2", "@uni-helper/pages-json-schema": "^0.2.28", "@uni-helper/uni-types": "1.0.0-alpha.6", "@uni-helper/unocss-preset-uni": "^0.2.11", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-pages": "^0.2.28", "@uni-helper/volar-service-uni-pages": "^0.2.28", "@unocss/eslint-config": "^66.3.3", "@unocss/preset-legacy-compat": "^65.4.2", "@vue/runtime-core": "^3.4.21", "@vue/tsconfig": "^0.1.3", "commitlint-config-git-commit-emoji": "^1.0.0", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import-x": "^4.6.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "eslint-plugin-vue-scoped-css": "^2.10.0", "globals": "^15.14.0", "lint-staged": "^15.4.1", "prettier": "3.4.2", "release-it": "^17.11.0", "rimraf": "^6.0.1", "sass": "^1.78.0", "sass-embedded": "^1.83.0", "stylelint": "^16.12.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "typescript": "^4.9.4", "typescript-eslint": "^8.18.2", "unocss": "0.65.2", "unocss-applet": "~0.7.8", "unplugin-auto-import": "^19.0.0", "vite": "5.2.8", "vite-plugin-restart": "^0.4.2", "vue-tsc": "^1.0.24"}}